# Development Guide

## Project Structure

```
├── src/
│   ├── core/                   # Core processing logic
│   │   ├── assistant.py        # Main assistant orchestrator
│   │   ├── config_manager.py   # Configuration management
│   │   ├── context_processor.py # Context awareness
│   │   ├── intent_manager.py   # Intent recognition
│   │   ├── learning_module.py  # Self-improvement
│   │   └── logger.py          # Logging setup
│   ├── ui/                     # User interface
│   │   ├── interface.py        # Main GUI
│   │   └── voice_interface.py  # Voice I/O
│   ├── actions/                # System automation
│   │   ├── action_executor.py  # Action coordination
│   │   ├── keyboard_controller.py # Keyboard automation
│   │   ├── mouse_controller.py # Mouse automation
│   │   ├── system_controller.py # System operations
│   │   ├── file_controller.py  # File operations
│   │   └── web_controller.py   # Web automation
│   ├── ai/                     # AI integration
│   │   └── openrouter_client.py # OpenRouter/Claude client
│   └── data/                   # Data management
│       └── database_manager.py # Database operations
├── config/                     # Configuration files
├── tests/                      # Test suites
├── docs/                       # Documentation
└── requirements.txt            # Dependencies
```

## Development Workflow

### 1. Setup Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd ai-assistant

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy configuration template
cp config/config.example.json config/config.json
```

### 2. Configuration

Edit `config/config.json`:
- Add your OpenRouter API key
- Adjust voice settings
- Configure automation parameters
- Set learning preferences

### 3. Running the Assistant

```bash
# Run the main application
python main.py

# Run with debug logging
python main.py --log-level DEBUG
```

### 4. Testing

```bash
# Run all tests
pytest

# Run specific test module
pytest tests/test_assistant.py

# Run with coverage
pytest --cov=src tests/
```

## Architecture Overview

### Core Components

1. **AIAssistant**: Main orchestrator that coordinates all components
2. **ConfigManager**: Handles configuration loading and validation
3. **ContextProcessor**: Maintains conversation context and memory
4. **IntentManager**: Analyzes user intent using Claude 3.7 Sonnet
5. **LearningModule**: Implements self-improvement mechanisms
6. **ActionExecutor**: Executes system actions with safety checks

### Action System

The action system is modular and extensible:

- **KeyboardController**: Handles typing, key presses, hotkeys
- **MouseController**: Manages mouse movements, clicks, scrolling
- **SystemController**: System operations (apps, processes, settings)
- **FileController**: File and directory operations
- **WebController**: Web browser automation

### Safety Mechanisms

1. **Action Confirmation**: Dangerous actions require user confirmation
2. **Safety Checks**: Built-in validation for potentially harmful operations
3. **Sandboxing**: Actions are executed in controlled environment
4. **Logging**: Comprehensive logging of all operations

## Adding New Features

### Adding a New Action Type

1. Create a new controller in `src/actions/`
2. Implement the controller interface
3. Register it in `ActionExecutor`
4. Add corresponding intent patterns

### Extending AI Capabilities

1. Modify the system prompt in `OpenRouterClient`
2. Add new intent patterns
3. Implement corresponding action handlers
4. Update the learning module to track new patterns

### Adding New UI Components

1. Create components in `src/ui/`
2. Integrate with the main interface
3. Add configuration options
4. Implement event handlers

## Code Standards

### Python Style

- Follow PEP 8
- Use type hints
- Document all public methods
- Maximum line length: 100 characters

### Error Handling

- Use specific exception types
- Log errors with context
- Provide user-friendly error messages
- Implement graceful degradation

### Testing

- Write unit tests for all components
- Use pytest fixtures for setup
- Mock external dependencies
- Aim for >80% code coverage

## Debugging

### Common Issues

1. **API Key Issues**: Check OpenRouter configuration
2. **Voice Recognition**: Verify microphone permissions
3. **Action Failures**: Check system permissions
4. **Database Errors**: Ensure write permissions in data directory

### Debug Mode

Enable debug logging in configuration:
```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

### Performance Monitoring

Monitor key metrics:
- Response time to user inputs
- API call latency
- Memory usage
- Action execution time

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request
5. Ensure CI passes

## Security Considerations

- Never log sensitive information
- Validate all user inputs
- Implement proper authentication for remote features
- Regular security audits of action permissions
