"""
Core AI Assistant class that orchestrates all components.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .context_processor import ContextProcessor
from .intent_manager import IntentManager
from .learning_module import LearningModule
from ..ai.deepseek_client import DeepSeekClient
from ..actions.action_executor import ActionExecutor
from ..data.database_manager import DatabaseManager


class AIAssistant:
    """Main AI Assistant class that coordinates all components."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the AI Assistant with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize core components
        self.db_manager = DatabaseManager(config['database'])
        self.deepseek_client = DeepSeekClient(
            config['deepseek'], self.db_manager)
        # Initialize context processor
        self.context_processor = ContextProcessor(
            self.db_manager, self.deepseek_client)  # Pass deepseek_client
        self.intent_manager = IntentManager(self.deepseek_client)
        self.action_executor = ActionExecutor(config['automation'])
        self.learning_module = LearningModule(
            self.db_manager, config['learning'])

        # State management
        self.is_running = False
        self.current_session_id = None

        self.logger.info("AI Assistant initialized")

    async def start(self):
        """Start the assistant."""
        self.is_running = True
        self.current_session_id = self._generate_session_id()
        self.logger.info(
            f"Assistant started with session ID: {self.current_session_id}")

        # Initialize database
        await self.db_manager.initialize()

        # Start learning module
        await self.learning_module.start()

    async def stop(self):
        """Stop the assistant."""
        self.is_running = False
        self.logger.info("Assistant stopping...")

        # Save session data
        if self.current_session_id:
            await self.context_processor.save_session(self.current_session_id)

        # Stop learning module
        await self.learning_module.stop()

        # Close database connections
        await self.db_manager.close()

        self.logger.info("Assistant stopped")

    async def process_input(self, user_input: str, input_type: str = "text") -> Dict[str, Any]:
        """
        Process user input and return response with actions.

        Args:
            user_input: The user's input (text or transcribed speech)
            input_type: Type of input ("text" or "voice")

        Returns:
            Dictionary containing response and actions to execute
        """
        try:
            self.logger.info(f"Processing {input_type} input: {user_input}")

            # Update context with user input
            context = await self.context_processor.update_context(
                user_input, self.current_session_id
            )

            # Analyze intent and generate response
            intent_result = await self.intent_manager.analyze_intent(
                user_input, context
            )

            # Execute actions if any
            action_results = []
            if intent_result.get('actions'):
                for action in intent_result['actions']:
                    result = await self.action_executor.execute_action(action)
                    action_results.append(result)

            # Extract topics and entities for context metadata
            assistant_metadata = {
                'topics': intent_result.get('topics', []),
                'entities': intent_result.get('entities', [])
            }

            # Update context with assistant response
            await self.context_processor.update_context(
                intent_result.get(
                    'response', "I'm sorry, I couldn't generate a response."),
                self.current_session_id,
                is_assistant=True,
                metadata=assistant_metadata
            )

            # Learn from this interaction
            await self.learning_module.record_interaction(
                user_input, intent_result, action_results, context
            )

            return {
                'response': intent_result['response'],
                'actions': action_results,
                'confidence': intent_result.get('confidence', 0.0),
                'requires_confirmation': intent_result.get('requires_confirmation', False)
            }

        except Exception as e:
            self.logger.error(f"Error processing input: {e}")
            return {
                'response': "I'm sorry, I encountered an error processing your request.",
                'actions': [],
                'confidence': 0.0,
                'requires_confirmation': False
            }

    async def confirm_action(self, action_id: str, confirmed: bool) -> Dict[str, Any]:
        """Handle user confirmation for actions."""
        if confirmed:
            # Execute the pending action
            result = await self.action_executor.execute_pending_action(action_id)
            return {
                'response': "Action executed successfully.",
                'action_result': result
            }
        else:
            # Cancel the action
            await self.action_executor.cancel_pending_action(action_id)
            return {
                'response': "Action cancelled.",
                'action_result': None
            }

    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def get_status(self) -> Dict[str, Any]:
        """Get current assistant status."""
        return {
            'running': self.is_running,
            'session_id': self.current_session_id,
            'components': {
                'database': self.db_manager.is_connected(),
                'deepseek': self.deepseek_client.is_available(),
                'learning': self.learning_module.is_active()
            }
        }
