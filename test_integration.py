#!/usr/bin/env python3
"""
Simple test script to verify DeepSeek API integration.
"""

from src.core.assistant import AIAssistant
from src.core.config_manager import ConfigManager
import os
import sys
import asyncio
import json
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


async def test_basic_functionality():
    """Test basic DeepSeek integration functionality."""
    print("🚀 Starting DeepSeek API Integration Test")
    print("=" * 50)

    try:
        # Load configuration
        print("📋 Loading configuration...")
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # Verify API key is configured
        if not config['deepseek']['api_key'] or config['deepseek']['api_key'] == "YOUR_DEEPSEEK_API_KEY_HERE":
            print("❌ Error: DeepSeek API key not configured!")
            print("Please update config/config.json with your API key.")
            return False

        print(f"✅ Configuration loaded successfully")
        print(f"   API Base URL: {config['deepseek']['base_url']}")
        print(f"   Model: {config['deepseek']['model']}")
        print(
            f"   Performance Monitoring: {config['deepseek']['performance_monitoring']}")

        # Initialize assistant
        print("\n🤖 Initializing AI Assistant...")
        assistant = AIAssistant(config)

        # Start assistant
        print("🔄 Starting assistant...")
        await assistant.start()
        print("✅ Assistant started successfully")

        # Test simple commands
        print("\n🧪 Testing simple commands...")

        test_inputs = [
            "hello",
            "how are you",
            "help"
            # Removed "what can you do for me?" as it requires API call
        ]

        for i, test_input in enumerate(test_inputs, 1):
            print(f"\n--- Test {i}: '{test_input}' ---")

            try:
                result = await assistant.process_input(test_input, "text")

                print(f"✅ Response: {result['response']}")
                print(f"   Confidence: {result.get('confidence', 'N/A')}")
                print(f"   Actions: {len(result.get('actions', []))}")

                # Small delay between requests
                await asyncio.sleep(1)

            except Exception as e:
                print(f"❌ Error processing input: {e}")
                return False

        # Test performance monitoring
        print("\n📊 Checking performance metrics...")
        if hasattr(assistant.deepseek_client, 'performance_monitor') and assistant.deepseek_client.performance_monitor:
            metrics = assistant.deepseek_client.performance_monitor.get_current_metrics()
            print(f"   Total requests: {metrics['total_requests']}")
            print(f"   Success rate: {metrics['success_rate']:.2%}")
            print(
                f"   Average response time: {metrics['avg_response_time']:.2f}s")

            # Check for alerts
            alerts = assistant.deepseek_client.performance_monitor.get_recent_alerts(
                5)
            if alerts:
                print(f"   Recent alerts: {len(alerts)}")
                for alert in alerts:
                    print(
                        f"     - {alert['severity'].upper()}: {alert['message']}")
            else:
                print("   No recent alerts")
        else:
            print("   Performance monitoring not enabled")

        # Test context processing
        print("\n💭 Testing context processing...")
        context = await assistant.context_processor.get_context(assistant.current_session_id)
        print(f"   Session ID: {context['session_id']}")
        print(f"   Message count: {context['message_count']}")
        print(f"   Topics: {context.get('topics', [])}")

        # Stop assistant
        print("\n🛑 Stopping assistant...")
        await assistant.stop()
        print("✅ Assistant stopped successfully")

        print("\n🎉 All tests completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_configuration():
    """Test configuration management."""
    print("\n🔧 Testing configuration management...")

    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # Validate required sections
        required_sections = ['deepseek', 'assistant', 'database', 'logging']
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing configuration section: {section}")
                return False

        # Validate DeepSeek configuration
        deepseek_config = config['deepseek']
        required_keys = ['api_key', 'base_url', 'model']
        for key in required_keys:
            if key not in deepseek_config:
                print(f"❌ Missing DeepSeek configuration key: {key}")
                return False

        print("✅ Configuration validation passed")
        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def test_database():
    """Test database functionality."""
    print("\n🗄️ Testing database functionality...")

    try:
        from src.data.database_manager import DatabaseManager

        # Test database initialization
        db_config = {"path": "data/test_assistant.db"}
        db_manager = DatabaseManager(db_config)
        await db_manager.initialize()

        print("✅ Database initialized successfully")

        # Check if database file exists
        if not Path(db_config['path']).exists():
            print("❌ Database file not created")
            return False

        print("✅ Database file created")

        # Test database connection
        if not db_manager.is_connected():
            print("❌ Database connection failed")
            return False

        print("✅ Database connection successful")

        # Clean up test database
        await db_manager.close()
        Path(db_config['path']).unlink(missing_ok=True)

        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


async def main():
    """Run all integration tests."""
    print("🔬 DeepSeek API Integration Test Suite")
    print("=" * 60)

    tests = [
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("Basic Functionality", test_basic_functionality),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        print("-" * 40)

        try:
            result = await test_func()
            results.append((test_name, result))

            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")

        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")

    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! DeepSeek integration is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the output above for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
