["tests/test_assistant.py::test_assistant_initialization", "tests/test_assistant.py::test_assistant_start_stop", "tests/test_assistant.py::test_process_input", "tests/test_assistant.py::test_session_management", "tests/test_deepseek_integration.py::TestContextProcessor::test_context_summarization", "tests/test_deepseek_integration.py::TestContextProcessor::test_context_update", "tests/test_deepseek_integration.py::TestDatabaseIntegration::test_database_initialization", "tests/test_deepseek_integration.py::TestDatabaseIntegration::test_table_creation", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_api_request_failure", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_api_request_success", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_caching", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_client_initialization", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_rate_limiting", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_session_management", "tests/test_deepseek_integration.py::TestDeepSeekClient::test_simple_commands", "tests/test_deepseek_integration.py::TestIntentManager::test_intent_analysis", "tests/test_deepseek_integration.py::TestIntentManager::test_intent_analysis_error"]