"""
Main user interface for the AI Assistant.
"""

import asyncio
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from typing import Dict, Any, Optional

from .voice_interface import VoiceInterface
from ..core.assistant import AIAssistant


class UserInterface:
    """Main GUI interface for the AI Assistant."""
    
    def __init__(self, assistant: AIAssistant, config: Dict[str, Any]):
        """Initialize the user interface."""
        self.assistant = assistant
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Voice interface
        self.voice_interface = VoiceInterface(config['voice']) if config['assistant']['voice_enabled'] else None
        
        # GUI components
        self.root = None
        self.chat_display = None
        self.input_entry = None
        self.status_label = None
        self.voice_button = None
        
        # State
        self.is_listening = False # For single-shot listening
        self.is_background_listening_active = False # For continuous listening
        self.is_running = False
        
        self.logger.info("User interface initialized")
    
    def run(self):
        """Start the user interface."""
        self.logger.info("Starting user interface...")
        
        # Start the assistant in a separate thread
        assistant_thread = threading.Thread(target=self._start_assistant, daemon=True)
        assistant_thread.start()
        
        # Create and run the GUI
        self._create_gui()
        self.root.mainloop()
    
    def _start_assistant(self):
        """Start the assistant in async context."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(self.assistant.start())
            self.is_running = True
            self._update_status("Assistant ready")
        except Exception as e:
            self.logger.error(f"Error starting assistant: {e}")
            self._update_status(f"Error: {e}")
    
    def _create_gui(self):
        """Create the GUI components."""
        self.root = tk.Tk()
        self.root.title("AI Assistant")
        self.root.geometry("800x600")
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="AI Assistant", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            main_frame, 
            wrap=tk.WORD, 
            width=70, 
            height=20,
            state=tk.DISABLED
        )
        self.chat_display.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Input frame
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)
        
        # Voice button
        if self.voice_interface:
            self.voice_button = ttk.Button(
                input_frame, 
                text="🎤", 
                command=self._toggle_voice, # This will now toggle single-shot listening
                width=3
            )
            self.voice_button.grid(row=0, column=0, padx=(0, 5))

            # Continuous listening button/toggle
            self.continuous_listen_button = ttk.Checkbutton(
                input_frame,
                text="Always On",
                command=self._toggle_continuous_listening
            )
            self.continuous_listen_button.grid(row=0, column=3, padx=(5,0)) # Adjust column as needed
        
        # Text input
        self.input_entry = ttk.Entry(input_frame, font=("Arial", 11))
        self.input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.input_entry.bind("<Return>", self._send_message)
        
        # Send button
        send_button = ttk.Button(input_frame, text="Send", command=self._send_message)
        send_button.grid(row=0, column=2)
        
        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="Initializing...", relief=tk.SUNKEN)
        self.status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Focus on input
        self.input_entry.focus()
        
        self.logger.info("GUI created successfully")

        # Automatically start background listening if configured (optional)
        # if self.voice_interface and self.config.get('voice', {}).get('start_continuous_listening_on_startup', False):
        #     self._toggle_continuous_listening() # This will start it if not active
    
    def _send_message(self, event=None):
        """Send a text message to the assistant."""
        message = self.input_entry.get().strip()
        if not message:
            return
        
        # Clear input
        self.input_entry.delete(0, tk.END)
        
        # Display user message
        self._add_message("You", message)
        
        # Process message in background
        threading.Thread(
            target=self._process_message, 
            args=(message, "text"), 
            daemon=True
        ).start()
    
    def _toggle_voice(self):
        """Toggle voice listening."""
        if not self.voice_interface:
            return
        
        if self.is_listening: # This refers to single-shot listening
            self._stop_listening() # Stop single-shot if active
        else:
            # If continuous listening is active, don't start single-shot
            if self.is_background_listening_active:
                self.logger.info("Continuous listening is active, single-shot mic toggle ignored.")
                messagebox.showinfo("Voice Input", "Continuous listening is active. Disable 'Always On' to use manual mic.")
                return
            self._start_listening() # Start single-shot
    
    def _start_listening(self):
        """Start voice listening (single-shot)."""
        if not self.voice_interface or self.is_listening or self.is_background_listening_active:
            return
        
        self.is_listening = True
        self.voice_button.config(text="🔴", state="disabled")
        self._update_status("Listening...")
        
        # Start listening in background
        threading.Thread(target=self._listen_for_voice, daemon=True).start()
    
    def _stop_listening(self):
        """Stop voice listening (single-shot)."""
        self.is_listening = False
        if self.voice_button:
            self.voice_button.config(text="🎤", state="normal")
        # Do not change status if background listening is taking over or was active
        if not self.is_background_listening_active:
            self._update_status("Assistant ready")
    
    def _listen_for_voice(self):
        """Listen for voice input (single-shot)."""
        try:
            text = self.voice_interface.listen()
            if text:
                # Display recognized text
                self.root.after(0, lambda: self._add_message("You (voice)", text))
                
                # Process the voice input
                self._process_message(text, "voice")
            
        except Exception as e:
            self.logger.error(f"Voice recognition error: {e}")
            self.root.after(0, lambda: self._update_status(f"Voice error: {e}"))
        
        finally:
            self.root.after(0, self._stop_listening)

    def _handle_recognized_speech(self, text: str):
        """Handles speech recognized from either single-shot or continuous listening."""
        self.logger.info(f"Handling recognized speech: {text}")
        # Display recognized text
        # Ensure this runs on the main Tkinter thread
        self.root.after(0, lambda: self._add_message("You (voice)", text))
        # Process the voice input
        # This already runs in a new thread via _process_message
        self._process_message(text, "voice")

    def _handle_listening_error(self, error: Exception):
        """Handles errors from the listening process."""
        self.logger.error(f"Voice recognition error (callback): {error}")
        self.root.after(0, lambda: self._update_status(f"Voice error: {error}"))
        # Optionally, try to restart background listening or inform the user
        if self.is_background_listening_active:
            # Maybe add a small delay and try to restart
            self.logger.info("Attempting to restart background listening after error.")
            # self.voice_interface.stop_background_listening() # Ensure it's stopped
            # self.voice_interface.start_background_listening(self._handle_recognized_speech, self._handle_listening_error)
            # For now, just update status and let user re-enable if needed
            self.root.after(0, lambda: self.continuous_listen_button.deselect())
            self.is_background_listening_active = False
            self._update_status("Continuous listening stopped due to error.")

    def _toggle_continuous_listening(self):
        """Toggles continuous background listening."""
        if not self.voice_interface:
            return

        if self.is_background_listening_active:
            self.voice_interface.stop_background_listening()
            self.is_background_listening_active = False
            self._update_status("Continuous listening disabled.")
            if self.voice_button: self.voice_button.config(state="normal") # Re-enable manual mic button
            self.logger.info("Continuous listening stopped by user.")
        else:
            if self.is_listening: # If single-shot is active, stop it first
                self._stop_listening()
            
            self.voice_interface.start_background_listening(
                on_speech_recognized_callback=self._handle_recognized_speech,
                on_listening_error_callback=self._handle_listening_error
            )
            self.is_background_listening_active = True
            self._update_status("Continuous listening enabled (Always On). Speak freely.")
            if self.voice_button: self.voice_button.config(state="disabled") # Disable manual mic button
            self.logger.info("Continuous listening started by user.")
    
    def _process_message(self, message: str, input_type: str):
        """Process a message through the assistant."""
        if not self.is_running:
            self.root.after(0, lambda: self._add_message("Assistant", "I'm not ready yet. Please wait..."))
            return
        
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Process the input
            result = loop.run_until_complete(
                self.assistant.process_input(message, input_type)
            )
            
            # Display response
            response = result.get('response', 'No response')
            self.root.after(0, lambda: self._add_message("Assistant", response))
            
            # Handle voice output
            if input_type == "voice" and self.voice_interface:
                threading.Thread(
                    target=lambda: self.voice_interface.speak(response),
                    daemon=True
                ).start()
            
            # Handle confirmation requests
            if result.get('requires_confirmation'):
                self.root.after(0, lambda: self._handle_confirmation(result))
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            self.root.after(0, lambda: self._add_message("Assistant", f"Error: {e}"))
    
    def _handle_confirmation(self, result: Dict[str, Any]):
        """Handle action confirmation requests."""
        action_id = result.get('action_id')
        if not action_id:
            return
        
        response = messagebox.askyesno(
            "Confirm Action",
            f"Do you want to execute this action?\n\n{result.get('message', 'Unknown action')}"
        )
        
        # Process confirmation in background
        threading.Thread(
            target=self._process_confirmation,
            args=(action_id, response),
            daemon=True
        ).start()
    
    def _process_confirmation(self, action_id: str, confirmed: bool):
        """Process action confirmation."""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.assistant.confirm_action(action_id, confirmed)
            )
            
            response = result.get('response', 'Action processed')
            self.root.after(0, lambda: self._add_message("Assistant", response))
            
        except Exception as e:
            self.logger.error(f"Error processing confirmation: {e}")
            self.root.after(0, lambda: self._add_message("Assistant", f"Confirmation error: {e}"))
    
    def _add_message(self, sender: str, message: str):
        """Add a message to the chat display."""
        self.chat_display.config(state=tk.NORMAL)
        self.chat_display.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def _update_status(self, status: str):
        """Update the status bar."""
        if self.status_label:
            self.status_label.config(text=status)
    
    def _on_closing(self):
        """Handle window closing event."""
        self.logger.info("Closing application...")
        if self.is_running:
            try:
                # Stop background listening if active
                if self.is_background_listening_active and self.voice_interface:
                    self.voice_interface.stop_background_listening()

                # Stop the assistant
                asyncio.run(self.assistant.stop())
            except Exception as e:
                self.logger.error(f"Error stopping assistant: {e}")
        
        # Close the window
        if self.root:
            self.root.destroy()
