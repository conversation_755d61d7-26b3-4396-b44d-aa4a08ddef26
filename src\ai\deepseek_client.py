"""
Advanced DeepSeek API client with recursive self-improvement capabilities.
Features: Adaptive learning, performance optimization, code generation, and autonomous enhancement.
"""

import asyncio
import json
import logging
import hashlib
import pickle
import statistics
import time
import traceback
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Callable, Union
import aiohttp
import ast
import inspect
import sys
from functools import wraps
import threading
import queue
from ..core.performance_monitor import PerformanceMonitor


class APIException(Exception):
    def __init__(self, message, status_code=None, details=None):
        super().__init__(message)
        self.status_code = status_code
        self.details = details


class AdaptiveRateLimiter:
    """Integrated rate limiter for DeepSeek client."""

    def __init__(self, base_delay: float = 1.0, max_retries: int = 3):
        self.base_delay = base_delay
        self.max_retries = max_retries
        self.request_history = deque(maxlen=100)
        self.error_patterns = defaultdict(int)
        self.success_rate = 1.0
        self.adaptive_multiplier = 1.0
        self.last_update = time.time()

        # DeepSeek API specific status codes
        self.status_multipliers = {
            429: 5.0,    # Rate limit - aggressive backoff
            503: 3.0,    # Service unavailable
            502: 2.5,    # Bad gateway
            500: 2.0,    # Internal server error
            408: 2.0,    # Timeout
        }

    def _calculate_delay(self, attempt: int, status_code: Optional[int] = None) -> float:
        """Calculate adaptive delay with exponential backoff."""
        status_multiplier = self.status_multipliers.get(status_code, 1.0)
        base_delay = self.base_delay * self.adaptive_multiplier
        exponential_delay = base_delay * (2 ** (attempt - 1))
        jittered_delay = exponential_delay * \
            (0.5 + (hash(str(time.time())) % 100) / 200)
        final_delay = jittered_delay * status_multiplier
        return min(final_delay, 60.0)  # Cap at 1 minute

    async def _update_stats(self, success: bool, status_code: Optional[int] = None, error: str = None):
        """Update internal statistics."""
        self.request_history.append({
            'timestamp': time.time(),
            'success': success,
            'status_code': status_code,
            'error': error
        })

        if not success and error:
            error_key = f"{status_code}_{error[:30]}"
            self.error_patterns[error_key] += 1

        # Update adaptive multiplier every 30 seconds
        now = time.time()
        if now - self.last_update > 30:
            await self._update_adaptive_parameters()

    async def _update_adaptive_parameters(self):
        """Update adaptive parameters based on recent performance."""
        now = time.time()
        self.last_update = now

        # Calculate success rate for last 2 minutes
        recent_requests = [
            req for req in self.request_history
            if now - req['timestamp'] < 120
        ]

        if len(recent_requests) >= 3:
            recent_successes = sum(
                1 for req in recent_requests if req['success'])
            self.success_rate = recent_successes / len(recent_requests)

            if self.success_rate > 0.85:
                self.adaptive_multiplier = max(
                    0.5, self.adaptive_multiplier * 0.9)
            elif self.success_rate < 0.6:
                self.adaptive_multiplier = min(
                    4.0, self.adaptive_multiplier * 1.3)


@dataclass
class PerformanceMetrics:
    """Performance tracking for self-improvement."""
    total_requests: int = 0
    successful_requests: int = 0
    total_latency: float = 0.0
    avg_latency: float = 0.0
    success_rate: float = 0.0
    requests_per_minute: float = 0.0
    error_patterns: Dict[str, int] = field(default_factory=dict)
    attempts_distribution: Dict[int, int] = field(
        default_factory=lambda: defaultdict(int))
    optimization_score: float = 0.0
    learning_rate: float = 0.1
    confidence_trend: List[float] = field(default_factory=list)
    api_efficiency: float = 0.0
    cost_effectiveness: float = 0.0
    user_satisfaction: float = 0.0
    # Added for _update_success_metrics
    request_times: deque = field(
        default_factory=lambda: deque(maxlen=100))  # To calculate RPM
    # To calculate RPM and other time-based metrics
    start_time: float = field(default_factory=time.time)


@dataclass
class LearningSnapshot:
    """Snapshot of learned optimizations."""
    timestamp: datetime
    optimization_rules: Dict[str, Any]
    performance_deltas: Dict[str, float]
    success_patterns: Dict[str, float]
    failure_patterns: Dict[str, float]
    adaptation_strategies: List[str]


class AdaptiveStrategy:
    """Dynamic strategy adaptation based on performance."""

    def __init__(self):
        self.strategies = {}
        self.performance_history = defaultdict(list)
        self.active_experiments = {}

    def register_strategy(self, name: str, implementation: Callable, weight: float = 1.0):
        """Register a new adaptive strategy."""
        self.strategies[name] = {
            'impl': implementation,
            'weight': weight,
            'success_count': 0,
            'total_count': 0,
            'avg_performance': 0.0
        }

    def evolve_strategy(self, name: str, performance_delta: float):
        """Evolve strategy based on performance feedback."""
        if name in self.strategies:
            strategy = self.strategies[name]
            strategy['total_count'] += 1
            if performance_delta > 0:
                strategy['success_count'] += 1

            # Update performance average
            old_avg = strategy['avg_performance']
            strategy['avg_performance'] = (old_avg + performance_delta) / 2

            # Adaptive weight adjustment
            success_rate = strategy['success_count'] / strategy['total_count']
            strategy['weight'] = min(
                2.0, max(0.1, success_rate * 1.5 + strategy['avg_performance'] * 0.5))


class CodeEvolutionEngine:
    """Self-modifying code engine for recursive improvement."""

    def __init__(self, client_instance):
        self.client = client_instance
        self.evolution_history = []
        self.generated_methods = {}
        self.optimization_queue = queue.Queue()
        self.evolution_lock = threading.Lock()

    def analyze_bottlenecks(self) -> Dict[str, Any]:
        """Analyze performance bottlenecks for optimization."""
        bottlenecks = {
            'slow_methods': [],
            'error_prone_sections': [],
            'optimization_opportunities': [],
            'resource_inefficiencies': []
        }

        # Analyze method performance
        for method_name, metrics in self.client.method_performance.items():
            if metrics['avg_time'] > 2.0:  # Methods taking >2s
                bottlenecks['slow_methods'].append({
                    'method': method_name,
                    'avg_time': metrics['avg_time'],
                    'call_count': metrics['call_count']
                })

        # Analyze error patterns
        for error_type, count in self.client.metrics.error_patterns.items():
            if count > 5:  # Frequent errors
                bottlenecks['error_prone_sections'].append({
                    'error_type': error_type,
                    'frequency': count
                })

        return bottlenecks

    def generate_optimization_code(self, bottleneck: Dict[str, Any]) -> Optional[str]:
        """Generate optimized code for identified bottlenecks."""
        if bottleneck['method'] == 'generate_response':
            return self._optimize_api_call_method()
        elif bottleneck['method'] == '_rate_limit':
            return self._optimize_rate_limiting()
        elif bottleneck['method'] == 'analyze_intent':
            return self._optimize_intent_analysis()

        return None

    def _optimize_api_call_method(self) -> str:
        """Generate optimized API call method."""
        return '''
    async def generate_response_optimized(self, *args, **kwargs):
        """Self-generated optimized response method."""
        # Implement connection pooling optimization
        if not hasattr(self, '_connection_pool'):
            self._connection_pool = aiohttp.TCPConnector(
                limit=20, limit_per_host=10, ttl_dns_cache=300
            )
        
        # Implement request batching for efficiency
        if hasattr(self, '_pending_requests'):
            if len(self._pending_requests) > 3:
                return await self._batch_process_requests()
        
        # Use original method with enhancements
        return await self.generate_response(*args, **kwargs)
        '''

    def apply_evolution(self, optimization_code: str, method_name: str):
        """Apply evolved code to the client instance."""
        try:
            with self.evolution_lock:
                # Compile and validate the new code
                compiled_code = compile(
                    optimization_code, f'<evolution_{method_name}>', 'exec')

                # Create new method namespace
                namespace = {'self': self.client,
                             'aiohttp': aiohttp, 'asyncio': asyncio}
                exec(compiled_code, namespace)

                # Extract and bind the new method
                for name, obj in namespace.items():
                    if callable(obj) and name.startswith(method_name):
                        setattr(self.client, name, obj.__get__(
                            self.client, type(self.client)))
                        self.generated_methods[name] = {
                            'code': optimization_code,
                            'timestamp': datetime.now(),
                            'performance_baseline': 0.0
                        }

                self.evolution_history.append({
                    'timestamp': datetime.now(),
                    'method': method_name,
                    'optimization': optimization_code[:200] + '...',
                    'reason': 'Performance optimization'
                })

        except Exception as e:
            self.client.logger.error(
                f"Evolution failed for {method_name}: {e}")


class DeepSeekClient:
    """Sophisticated DeepSeek client with recursive self-improvement."""

    def __init__(self, config: Dict[str, Any], db_manager=None):
        """Initialize the advanced DeepSeek client."""
        self.config = config
        self.api_key = config['api_key']
        self.base_url = config['base_url']
        self.model = config['model']
        self.logger = self._setup_advanced_logging()

        # Initialize performance monitoring if enabled
        self.performance_monitoring_enabled = config.get(
            'performance_monitoring', True)
        self.performance_monitor = None
        if self.performance_monitoring_enabled and db_manager:
            monitor_config = {
                'response_time_threshold': config.get('response_time_threshold', 5.0),
                'error_rate_threshold': config.get('error_rate_threshold', 0.1),
                'success_rate_threshold': config.get('success_rate_threshold', 0.9)
            }
            self.performance_monitor = PerformanceMonitor(
                db_manager, monitor_config)

        # Initialize rate limiting and performance attributes
        self.timeout = config.get('timeout', 30.0)
        self.timeout_multiplier = 1.0
        self.base_delay = 1.0
        self.backoff_factor = 2.0
        self.jitter = True
        self.circuit_breaker_enabled = False
        self.health_check_interval = 60.0
        self.max_retries = config.get('max_retries', 3)
        self.min_request_interval = config.get(
            'min_request_interval', 0.5)  # Added min_request_interval

        # Initialize rate limiter
        self.rate_limiter = AdaptiveRateLimiter(
            base_delay=self.base_delay, max_retries=self.max_retries)

        # Performance tracking
        self.perf_tracker = {}
        # self.request_count = 0 # Covered by self.metrics.total_requests
        # self.error_count = 0 # Covered by len(self.metrics.error_patterns)
        # self.last_request_time = None # Covered by self.metrics.request_times
        self.metrics = PerformanceMetrics()  # Initialize once
        # Alias for _update_success_metrics compatibility
        self.performance_metrics = self.metrics
        self.method_performance = defaultdict(
            lambda: {'avg_time': 0.0, 'call_count': 0})
        # self.total_time = 0.0 # Covered by self.metrics.total_latency or similar

        # Session management
        self.session = None

        # Core components
        # self.session = None # Redundant initialization
        self.response_cache = {}
        self.cache_access_times = {}
        # Potentially redundant with metrics.error_patterns
        self.error_counts = defaultdict(int)
        # self.request_times = deque(maxlen=100) # Covered by self.metrics.request_times
        # self.success_count = 0 # Covered by self.metrics.successful_requests
        # self.total_requests = 0 # Covered by self.metrics.total_requests

        # Rate limiting components (some might be redundant with AdaptiveRateLimiter or PerformanceMetrics)
        # self.last_request_time = 0 # Redundant
        # self.request_count = 0 # Redundant
        # 60 seconds window - review if used or covered by AdaptiveRateLimiter
        self.rate_limit_window = 60
        # review if used or covered by AdaptiveRateLimiter
        self.max_requests_per_window = 50
        # self.backoff_factor = 1.5 # Potentially part of AdaptiveRateLimiter logic
        # self.max_retries = 5 # Already set from config

        # Initialize strategies
        self.adaptive_strategy = AdaptiveStrategy()
        # self.method_performance = defaultdict(lambda: {'avg_time': 0.0, 'call_count': 0}) # Already initialized

        # Initialize strategies
        self._initialize_strategies()

    def _initialize_strategies(self):
        """Initialize various operational strategies."""
        self._aggressive_retry_strategy = {
            'max_retries': 5,
            'backoff_factor': 2.0,
            'initial_delay': 1.0
        }

        self._conservative_backoff_strategy = {
            'base_delay': 1.0,
            'max_delay': 30.0,
            'jitter': 0.1
        }

        self._intelligent_caching_strategy = {
            'ttl': 300,  # 5 minutes
            'max_size': 1000,
            'similarity_threshold': 0.85
        }

        self._dynamic_timeout_strategy = {
            'base_timeout': 10.0,
            'max_timeout': 30.0,
            'success_rate_threshold': 0.8
        }

    def _check_rate_limit(self) -> Tuple[bool, float]:
        """Check if we're within rate limits and calculate delay if needed."""
        current_time = time.time()
        window_start = current_time - self.rate_limit_window

        # Clean up old request times
        while self.request_times and self.request_times[0] < window_start:
            self.request_times.popleft()

        # Check if we're at the limit
        if len(self.request_times) >= self.max_requests_per_window:
            oldest_request = self.request_times[0]
            delay_needed = self.rate_limit_window - \
                (current_time - oldest_request)
            return False, max(0, delay_needed)

        return True, 0.0

    def _update_rate_limit_metrics(self):
        """Update rate limiting metrics after a request."""
        current_time = time.time()
        self.request_times.append(current_time)
        self.last_request_time = current_time
        self.request_count += 1
        self.session: Optional[aiohttp.ClientSession] = None
        self.metrics = PerformanceMetrics()
        self.adaptive_strategy = AdaptiveStrategy()
        self.evolution_engine = CodeEvolutionEngine(self)

        # Advanced caching with TTL and LRU
        self.response_cache = {}
        self.cache_ttl = {}
        self.cache_max_size = 200
        self.cache_access_times = {}

        # Performance tracking
        self.method_performance = defaultdict(
            lambda: {'call_count': 0, 'total_time': 0, 'avg_time': 0})
        self.request_history = deque(maxlen=1000)
        self.learning_snapshots = []

        # Dynamic adaptation parameters
        self.min_request_interval = config.get('min_request_interval', 3.0)
        self.max_retries = config.get('max_retries', 5)
        self.timeout_multiplier = config.get('timeout_multiplier', 1.0)
        self.success_threshold = config.get('success_threshold', 0.8)

        # Performance tracking
        self.total_time = 0.0
        self.total_requests = 0
        self.request_times = deque(maxlen=1000)

        # Self-improvement scheduling
        self.improvement_scheduler = asyncio.create_task(
            self._continuous_improvement_loop()) if asyncio.get_event_loop().is_running() else None

        # Initialize adaptive strategies
        self._initialize_strategies()

    def _setup_advanced_logging(self) -> logging.Logger:
        """Setup sophisticated logging with performance tracking."""
        logger = logging.getLogger(f"{__name__}.{id(self)}")
        logger.setLevel(logging.DEBUG)

        # Create custom formatter with performance metrics
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [Perf: %(performance)s] - %(message)s',
            defaults={'performance': 'N/A'}
        )

        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        return logger

    def _initialize_strategies(self):
        """Initialize adaptive strategies for different scenarios."""
        self.adaptive_strategy.register_strategy(
            'aggressive_retry',
            self._aggressive_retry_strategy,
            weight=1.2
        )
        self.adaptive_strategy.register_strategy(
            'conservative_backoff',
            self._conservative_backoff_strategy,
            weight=0.8
        )
        self.adaptive_strategy.register_strategy(
            'intelligent_caching',
            self._intelligent_caching_strategy,
            weight=1.5
        )
        self.adaptive_strategy.register_strategy(
            'dynamic_timeout',
            self._dynamic_timeout_strategy,
            weight=1.0
        )

    def _aggressive_retry_strategy(self, attempt: int, status_code: int) -> float:
        """Aggressive retry strategy with minimal delays."""
        return max(1.0, attempt * 2.0)

    def _conservative_backoff_strategy(self, attempt: int, status_code: int) -> float:
        """Conservative backoff with longer delays."""
        return min(30.0, (attempt ** 2) * 3.0)

    def _intelligent_caching_strategy(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Intelligent caching strategy for responses."""
        cache_key = self._generate_cache_key(request_data)
        if cache_key in self.response_cache:
            # 5 minutes TTL
            if time.time() - self.cache_access_times[cache_key] < 300:
                return self.response_cache[cache_key]
            else:
                del self.response_cache[cache_key]
                del self.cache_access_times[cache_key]
        return None

    def _dynamic_timeout_strategy(self, attempt: int) -> float:
        """Dynamic timeout strategy based on performance history."""
        base_timeout = 10.0
        if self.metrics.success_rate > 0.9:
            return base_timeout * 1.5
        elif self.metrics.success_rate < 0.5:
            return base_timeout * 3.0
        return base_timeout * (1.0 + attempt * 0.5)

    def _generate_cache_key(self, request_data: Dict[str, Any]) -> str:
        """Generate a unique cache key for request data."""
        serialized = json.dumps(request_data, sort_keys=True)
        return hashlib.md5(serialized.encode()).hexdigest()

    async def analyze_intent(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user input to determine intent and generate appropriate response."""
        start_time = time.time()
        if not self.session:
            await self.start()

        # Check cache first
        cache_key = self._generate_cache_key({'input': user_input})
        if cached_response := self._intelligent_caching_strategy({'input': user_input}):
            self.logger.info("Using cached response for similar input")
            cached_response['total_time'] = time.time() - start_time
            # Ensure 'response' key exists, even if it's from a fallback in the cached item
            if 'response' not in cached_response and 'fallback_response' in cached_response:
                cached_response['response'] = cached_response['fallback_response']
            elif 'response' not in cached_response:
                cached_response['response'] = "Using cached response."
            return cached_response

        # Handle simple commands first
        simple_response = self._handle_simple_commands(user_input)
        if simple_response is not None:
            simple_response['total_time'] = time.time() - start_time
            # Ensure 'response' key exists for simple commands
            if 'response' not in simple_response:
                simple_response['response'] = "Command processed."
            return simple_response

        try:
            # Generate response using the model
            messages = [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": user_input}
            ]
            api_response = await self.generate_response(messages)
            total_time = time.time() - start_time

            if api_response.get("success", False):
                # Ensure 'response' key exists from API or provide a default
                final_response = {
                    **api_response,
                    'total_time': total_time,
                    'response': api_response.get('response', api_response.get('fallback_response', 'Successfully processed.'))
                }
                # Cache successful responses
                self.response_cache[cache_key] = final_response
                self.cache_access_times[cache_key] = time.time()
                return final_response
            else:
                # Ensure 'total_time' is correctly named and present
                return {
                    "success": False,
                    "error": api_response.get("error", "Failed to analyze intent"),
                    "response": api_response.get("fallback_response", "I'm having trouble understanding that right now. Could you rephrase it?"),
                    'total_time': total_time
                }

        except Exception as e:
            self.logger.error(f"Error in analyze_intent: {str(e)}")
            # Ensure 'total_time' is correctly named and present in all return paths
            return {
                "success": False,
                "error": str(e),
                "response": "I encountered an error processing your request. Please try again.",
                'total_time': time.time() - start_time  # Ensuring correct key name
            }

    def _handle_simple_commands(self, user_input: str) -> Optional[Dict[str, Any]]:
        """Handle simple commands that don't require API calls."""
        input_lower = user_input.lower().strip()

        # Simple greeting patterns
        greetings = ["hello", "hi", "hey", "greetings"]
        if any(input_lower.startswith(greeting) for greeting in greetings):
            return {
                "success": True,
                "response": "Hello! How can I help you today?",
                "intent": "greeting"
            }

        # Status check
        if "how are you" in input_lower:
            return {
                "success": True,
                "response": "I'm functioning well and ready to assist you!",
                "intent": "status_check"
            }

        # Help request
        if input_lower in ["help", "what can you do", "commands"]:
            return {
                "success": True,
                "response": "I can help you with various tasks. Just ask me anything!",
                "intent": "help_request"
            }

        return None

    def _handle_adaptive_error(self, error: Exception, attempt: int) -> Dict[str, Any]:
        """Handle errors adaptively based on type and attempt count."""
        error_str = str(error)
        self.logger.error(
            f"[Perf: N/A] - Error on attempt {attempt}: {error_str}")

        # Update error metrics
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(
            error_type, 0) + 1

        # Adjust strategies based on error patterns
        if self.error_counts[error_type] > 5:
            self._aggressive_retry_strategy['max_retries'] = min(
                10, self._aggressive_retry_strategy['max_retries'] + 1)

        # Provide appropriate fallback response
        if isinstance(error, (asyncio.TimeoutError, aiohttp.ClientTimeout)):
            return {
                "success": False,
                "error": "Request timed out",
                "fallback_response": "I'm taking longer than expected to respond. Please try again."
            }
        elif isinstance(error, aiohttp.ClientConnectorError):
            return {
                "success": False,
                "error": "Connection error",
                "fallback_response": "I'm having trouble connecting to my services. Please check your internet connection."
            }
        else:
            return {
                "success": False,
                "error": error_str,
                "fallback_response": "I encountered an unexpected error. Please try again."
            }

    def performance_monitor(func):
        """Decorator for monitoring method performance."""
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            start_time = time.time()
            method_name = func.__name__

            try:
                result = await func(self, *args, **kwargs)
                success = True
                error = None
            except Exception as e:
                result = None
                success = False
                error = str(e)
                raise
            finally:
                end_time = time.time()
                duration = end_time - start_time

                # Update performance metrics
                perf = self.method_performance[method_name]
                perf['call_count'] += 1
                perf['total_time'] += duration
                perf['avg_time'] = perf['total_time'] / perf['call_count']

                # Log performance
                self.logger.info(
                    f"Method {method_name} completed in {duration:.3f}s",
                    extra={'performance': f"{duration:.3f}s"}
                )

                # Trigger self-improvement if performance degrades
                if duration > perf['avg_time'] * 1.5:  # 50% slower than average
                    asyncio.create_task(
                        self._trigger_optimization(method_name, duration))

            return result
        return wrapper

    async def __aenter__(self):
        """Enhanced async context manager entry."""
        await self.start()
        # Start continuous improvement if not already running
        if not self.improvement_scheduler or self.improvement_scheduler.done():
            self.improvement_scheduler = asyncio.create_task(
                self._continuous_improvement_loop())
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Enhanced async context manager exit."""
        # Cancel improvement scheduler
        if self.improvement_scheduler and not self.improvement_scheduler.done():
            self.improvement_scheduler.cancel()
            try:
                await self.improvement_scheduler
            except asyncio.CancelledError:
                pass

        await self.close()
        await self._save_learning_state()

    async def start(self):
        """Enhanced session initialization with adaptive configuration."""
        if not self.session or self.session.closed:
            # Adaptive connector configuration based on past performance
            connector_config = self._get_adaptive_connector_config()

            connector = aiohttp.TCPConnector(**connector_config)

            timeout_config = aiohttp.ClientTimeout(
                total=30 * self.timeout_multiplier,
                connect=10 * self.timeout_multiplier,
                sock_read=20 * self.timeout_multiplier
            )

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_config,
                headers=self._get_adaptive_headers()
            )

    def _get_adaptive_connector_config(self) -> Dict[str, Any]:
        """Generate adaptive connector configuration based on performance history."""
        base_config = {
            'limit': 15,
            'limit_per_host': 8,
            'keepalive_timeout': 45,
            'enable_cleanup_closed': True,
            'ttl_dns_cache': 300
        }

        # Adapt based on recent success rates
        if self.metrics.success_rate > 0.9:
            # Increase concurrency for good performance
            base_config['limit'] = 25
            base_config['limit_per_host'] = 12
        elif self.metrics.success_rate < 0.7:
            # Reduce concurrency for poor performance
            base_config['limit'] = 8
            base_config['limit_per_host'] = 4

        return base_config

    def _get_adaptive_headers(self) -> Dict[str, str]:
        """Generate adaptive headers based on API performance."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": f"AdvancedDeepSeekClient/2.0 (Adaptive; Success: {self.metrics.success_rate:.2f})"
        }

        # Add performance-based headers
        if self.metrics.success_rate > 0.95:
            headers["X-Priority"] = "high"
        elif self.metrics.success_rate < 0.5:
            headers["X-Retry-Strategy"] = "conservative"

        return headers

    async def close(self):
        """Enhanced session cleanup."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    @performance_monitor
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        max_tokens: int = 300,
        temperature: float = 0.3,
        retry_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Advanced response generation with self-adapting parameters.
        """
        # Use adaptive retry count if not specified
        if retry_count is None:
            retry_count = self._calculate_adaptive_retry_count()

        # Apply intelligent pre-processing
        messages = await self._preprocess_messages(messages)

        attempts = 0
        last_error = None
        start_time = time.time()

        while attempts <= retry_count:
            try:
                if not self.session or self.session.closed:
                    await self.start()

                # Adaptive rate limiting
                await self._adaptive_rate_limit(attempts)

                # Construct payload with dynamic optimizations
                payload = await self._construct_adaptive_payload(
                    messages, system_prompt, max_tokens, temperature
                )

                self.logger.info(
                    f"API request attempt {attempts+1}/{retry_count+1}")

                # Execute request with adaptive timeout
                async with self.session.post(
                    f"{self.base_url}/chat/completions",
                    json=payload
                ) as response:

                    result = await self._process_response(response, start_time)

                    if result["success"]:
                        # Update success metrics
                        # Made async
                        await self._update_success_metrics(time.time() - start_time, attempts)

                        # Record performance metrics
                        if self.performance_monitor:
                            await self.performance_monitor.record_api_call(
                                endpoint="chat/completions",
                                method="POST",
                                response_time=time.time() - start_time,
                                status_code=response.status,
                                success=True,
                                model_used=self.model,
                                tokens_used=result.get(
                                    "usage", {}).get("total_tokens")
                            )

                        # Apply post-processing optimizations
                        return await self._postprocess_response(result)
                    else:
                        # Record failed request metrics
                        if self.performance_monitor:
                            await self.performance_monitor.record_api_call(
                                endpoint="chat/completions",
                                method="POST",
                                response_time=time.time() - start_time,
                                status_code=response.status,
                                success=False,
                                error_message=result.get("error"),
                                model_used=self.model
                            )

                        # Analyze failure and adapt strategy
                        await self._analyze_failure(response.status, result.get("error"))
                        attempts += 1
                        last_error = result.get("error")

                        # Apply adaptive backoff
                        delay = await self._calculate_adaptive_backoff(attempts, response.status)
                        await asyncio.sleep(delay)
                        continue

            except Exception as e:
                attempts += 1
                last_error = str(e)
                self.logger.warning(f"Request attempt {attempts} failed: {e}")

                # Adaptive error handling
                await self._handle_adaptive_error(e, attempts)
                continue

        # All attempts failed - learn from failure
        await self._learn_from_failure(last_error, retry_count)

        return {
            "success": False,
            "error": f"Failed after {retry_count+1} attempts. Adaptive learning engaged. Last error: {last_error}",
            "timestamp": datetime.now().isoformat(),
            "adaptation_applied": True
        }

    async def _update_success_metrics(self, duration: float, attempts: int):
        """Update metrics upon successful API call."""
        self.metrics.total_requests += 1
        self.metrics.successful_requests += 1
        self.metrics.total_latency += duration
        self.metrics.avg_latency = self.metrics.total_latency / \
            self.metrics.successful_requests if self.metrics.successful_requests > 0 else 0
        self.metrics.success_rate = self.metrics.successful_requests / \
            self.metrics.total_requests if self.metrics.total_requests > 0 else 0
        self.metrics.requests_per_minute = self.metrics.total_requests / \
            ((time.time() - self.metrics.start_time) /
             60) if (time.time() - self.metrics.start_time) > 0 else 0
        # Update attempts distribution
        if attempts + 1 not in self.metrics.attempts_distribution:
            self.metrics.attempts_distribution[attempts + 1] = 0
        self.metrics.attempts_distribution[attempts + 1] += 1
        self.logger.info(
            f"Success metrics updated. Latency: {duration:.2f}s, Attempts: {attempts + 1}")

        # Potentially trigger learning/adaptation based on metrics
        if self.metrics.total_requests % 10 == 0:  # Every 10 requests
            await self.adaptive_strategy.adapt(self.metrics, self.method_performance)
            self.logger.info(
                "Adaptive strategy reviewed based on recent performance.")

    async def _preprocess_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Intelligent message preprocessing based on learned patterns."""
        processed_messages = []

        for msg in messages:
            # Apply content optimization based on success patterns
            optimized_content = await self._optimize_content(msg["content"])
            processed_messages.append({
                "role": msg["role"],
                "content": optimized_content
            })

        return processed_messages

    async def _optimize_content(self, content: str) -> str:
        """Optimize content based on learned successful patterns."""
        # Apply learned optimizations
        if len(content) > 1000:  # Long content optimization
            content = await self._compress_content_intelligently(content)

        # Apply successful prompt patterns
        if hasattr(self, '_successful_patterns'):
            for pattern, replacement in self._successful_patterns.items():
                content = content.replace(pattern, replacement)

        return content

    async def _compress_content_intelligently(self, content: str) -> str:
        """Intelligently compress content while preserving meaning."""
        # Implement smart compression based on learned importance weights
        sentences = content.split('. ')
        if len(sentences) > 10:
            # Keep first 3, last 2, and most important middle sentences
            important_middle = self._extract_important_sentences(
                sentences[3:-2], 3)
            compressed = '. '.join(
                sentences[:3] + important_middle + sentences[-2:])
            return compressed + '.'
        return content

    def _extract_important_sentences(self, sentences: List[str], count: int) -> List[str]:
        """Extract most important sentences based on learned criteria."""
        # Score sentences based on learned importance patterns
        scored_sentences = []
        for sentence in sentences:
            score = self._calculate_sentence_importance(sentence)
            scored_sentences.append((sentence, score))

        # Return top N sentences maintaining order
        important = sorted(
            scored_sentences, key=lambda x: x[1], reverse=True)[:count]
        return [sent[0] for sent in sorted(important, key=lambda x: sentences.index(x[0]))]

    def _calculate_sentence_importance(self, sentence: str) -> float:
        """Calculate sentence importance based on learned patterns."""
        importance = 0.0

        # Keywords that historically led to successful responses
        important_keywords = ['analyze', 'generate',
                              'create', 'explain', 'optimize']
        for keyword in important_keywords:
            if keyword in sentence.lower():
                importance += 0.2

        # Length bonus (medium length sentences are often more important)
        length_score = 1.0 - abs(len(sentence) - 50) / 100
        importance += max(0, length_score) * 0.3

        return importance

    async def _construct_adaptive_payload(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str],
        max_tokens: int,
        temperature: float
    ) -> Dict[str, Any]:
        """Construct API payload with adaptive optimizations."""

        # Adaptive parameter adjustment based on historical performance
        adapted_temperature = self._adapt_temperature(temperature)
        adapted_max_tokens = self._adapt_max_tokens(max_tokens)

        payload = {
            "model": self.model,
            "messages": messages.copy(),
            "max_tokens": adapted_max_tokens,
            "temperature": adapted_temperature,
            "stream": False
        }

        # Add system prompt if provided
        if system_prompt:
            optimized_system_prompt = await self._optimize_system_prompt(system_prompt)
            payload["messages"].insert(0, {
                "role": "system",
                "content": optimized_system_prompt
            })

        # Add adaptive parameters based on learned preferences
        if self.metrics.success_rate > 0.9:
            # Allow more creativity when performing well
            payload["top_p"] = 0.95
        elif self.metrics.success_rate < 0.7:
            payload["top_p"] = 0.8   # Be more conservative when struggling

        return payload

    def _adapt_temperature(self, base_temperature: float) -> float:
        """Adapt temperature based on recent performance."""
        if self.metrics.success_rate > 0.9:
            return min(1.0, base_temperature * 1.1)  # Slightly more creative
        elif self.metrics.success_rate < 0.6:
            return max(0.0, base_temperature * 0.8)  # More conservative
        return base_temperature

    def _adapt_max_tokens(self, base_max_tokens: int) -> int:
        """Adapt max tokens based on response quality patterns."""
        if hasattr(self, '_optimal_token_range'):
            optimal_min, optimal_max = self._optimal_token_range
            if base_max_tokens < optimal_min:
                return optimal_min
            elif base_max_tokens > optimal_max:
                return optimal_max
        return base_max_tokens

    async def _optimize_system_prompt(self, system_prompt: str) -> str:
        """Optimize system prompt based on successful patterns."""
        # Apply learned system prompt optimizations
        optimizations = [
            ("Be brief", "Provide concise, focused responses"),
            ("JSON format", "Return valid JSON with proper structure"),
            ("Analyze", "Thoroughly analyze and provide detailed insights")
        ]

        optimized = system_prompt
        for old_pattern, new_pattern in optimizations:
            if old_pattern in optimized and self._pattern_success_rate(old_pattern) < 0.7:
                optimized = optimized.replace(old_pattern, new_pattern)

        return optimized

    def _pattern_success_rate(self, pattern: str) -> float:
        """Calculate success rate for a specific pattern."""
        # This would be implemented with actual tracking
        return 0.8  # Placeholder

    async def _make_api_request(self, endpoint: str, payload: Dict) -> Dict:
        """Make HTTP request to DeepSeek API with rate limiting and retries."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        url = f"{self.base_url}/{endpoint}"
        start_time = time.time()

        for attempt in range(1, self.rate_limiter.max_retries + 1):
            if attempt > 1:
                delay = self.rate_limiter._calculate_delay(attempt - 1)
                await asyncio.sleep(delay)

            try:
                if not self.session:
                    timeout = aiohttp.ClientTimeout(
                        total=self.timeout * self.timeout_multiplier)
                    self.session = aiohttp.ClientSession(timeout=timeout)

                self.request_count += 1
                self.last_request_time = time.time()

                async with self.session.post(url, json=payload, headers=headers) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        self.error_count += 1
                        await self.rate_limiter._update_stats(
                            success=False,
                            status_code=response.status,
                            error=error_text
                        )
                        if attempt == self.rate_limiter.max_retries:
                            raise APIException(
                                f"API Error: {response.status}", response.status, error_text)
                        continue

                    result = await response.json()
                    await self.rate_limiter._update_stats(success=True)

                    # Update performance metrics
                    elapsed = time.time() - start_time
                    self.method_performance['_make_api_request']['avg_time'] = (
                        (self.method_performance['_make_api_request']['avg_time'] *
                         self.method_performance['_make_api_request']['call_count'] + elapsed) /
                        (self.method_performance['_make_api_request']
                         ['call_count'] + 1)
                    )
                    self.method_performance['_make_api_request']['call_count'] += 1

                    return result

            except asyncio.TimeoutError:
                self.error_count += 1
                await self.rate_limiter._update_stats(
                    success=False,
                    status_code=408,
                    error="Request timed out"
                )
                if attempt == self.rate_limiter.max_retries:
                    raise APIException("Request timeout",
                                       408, "Request timed out")
            except aiohttp.ClientError as e:
                self.error_count += 1
                await self.rate_limiter._update_stats(
                    success=False,
                    status_code=None,
                    error=str(e)
                )
                if attempt == self.rate_limiter.max_retries:
                    raise APIException(f"Connection error: {e}", 0, str(e))

        raise APIException("Max retries exceeded", 0,
                           "All retry attempts failed")

    async def _process_response(self, response: aiohttp.ClientResponse, start_time: float) -> Dict[str, Any]:
        """Process API response with adaptive handling."""

        if response.status == 200:
            try:
                data = await response.json()

                # Validate response structure
                if not self._validate_response_structure(data):
                    return {
                        "success": False,
                        "error": "Invalid response structure received from API"
                    }

                # Extract and enhance response
                content = data["choices"][0]["message"]["content"]
                enhanced_content = await self._enhance_response_content(content)

                return {
                    "success": True,
                    "content": enhanced_content,
                    "usage": data.get("usage", {}),
                    "model": data.get("model", self.model),
                    "timestamp": datetime.now().isoformat(),
                    "response_time": time.time() - start_time,
                    "enhancements_applied": True
                }

            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "error": f"Failed to parse JSON response: {e}"
                }

        else:
            error_text = await response.text()
            return {
                "success": False,
                "error": f"API error {response.status}: {error_text}",
                "status_code": response.status
            }

    def _validate_response_structure(self, data: Dict[str, Any]) -> bool:
        """Validate API response structure."""
        required_fields = ["choices"]
        for field in required_fields:
            if field not in data:
                return False

        if not data["choices"] or "message" not in data["choices"][0]:
            return False

        return True

    async def _enhance_response_content(self, content: str) -> str:
        """Enhance response content based on learned improvements."""
        # Apply learned content enhancements
        enhanced = content

        # Fix common formatting issues learned from past responses
        enhanced = self._apply_formatting_fixes(enhanced)

        # Add context improvements if beneficial
        if self._should_add_context_enhancement(content):
            enhanced = self._add_context_enhancement(enhanced)

        return enhanced

    def _apply_formatting_fixes(self, content: str) -> str:
        """Apply learned formatting fixes."""
        fixes = [
            (r'\n\n+', '\n\n'),  # Remove excessive newlines
            (r'  +', ' '),        # Remove excessive spaces
        ]

        import re
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)

        return content.strip()

    def _should_add_context_enhancement(self, content: str) -> bool:
        """Determine if content enhancement would be beneficial."""
        # Based on learned patterns
        return len(content) < 50 or "JSON" in content

    def _add_context_enhancement(self, content: str) -> str:
        """Add context enhancement to improve response quality."""
        if "JSON" in content.upper() and not content.strip().startswith('{'):
            return f"Here's the requested information in JSON format:\n\n{content}"
        return content

    async def _postprocess_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply post-processing optimizations to successful responses."""
        # Cache successful responses for future optimization
        await self._cache_successful_response(response)

        # Extract patterns for future learning
        await self._extract_success_patterns(response)

        return response

    async def _cache_successful_response(self, response: Dict[str, Any]):
        """Cache successful response with intelligent TTL."""
        content_hash = hashlib.md5(response["content"].encode()).hexdigest()

        # Adaptive TTL based on content type and success rate
        base_ttl = 3600  # 1 hour base
        if self.metrics.success_rate > 0.9:
            ttl = base_ttl * 2  # Cache longer when performing well
        else:
            ttl = base_ttl // 2  # Shorter cache when struggling

        self.response_cache[content_hash] = response
        self.cache_ttl[content_hash] = datetime.now() + timedelta(seconds=ttl)
        self.cache_access_times[content_hash] = datetime.now()

    async def _extract_success_patterns(self, response: Dict[str, Any]):
        """Extract patterns from successful responses for learning."""
        content = response["content"]

        # Extract structural patterns
        if content.startswith('{') and content.endswith('}'):
            if not hasattr(self, '_json_success_count'):
                self._json_success_count = 0
            self._json_success_count += 1

        # Extract length patterns
        content_length = len(content)
        if not hasattr(self, '_successful_lengths'):
            self._successful_lengths = []
        self._successful_lengths.append(content_length)

        # Keep only recent patterns (last 100)
        if len(self._successful_lengths) > 100:
            self._successful_lengths = self._successful_lengths[-100:]

    def _calculate_adaptive_retry_count(self) -> int:
        """Calculate retry count based on current performance metrics."""
        base_retries = 3

        if self.metrics.success_rate > 0.9:
            # Fewer retries when performing well
            return max(1, base_retries - 1)
        elif self.metrics.success_rate < 0.5:
            return min(7, base_retries + 2)  # More retries when struggling

        return base_retries

    async def _adaptive_rate_limit(self, attempt_number: int):
        """Implement adaptive rate limiting based on performance and attempt number."""
        base_interval = self.min_request_interval

        # Increase interval based on attempt number
        attempt_multiplier = 1 + (attempt_number * 0.5)

        # Adjust based on recent success rate
        if self.metrics.success_rate > 0.9:
            success_multiplier = 0.8  # Faster when performing well
        elif self.metrics.success_rate < 0.6:
            success_multiplier = 1.5  # Slower when struggling
        else:
            success_multiplier = 1.0

        adaptive_interval = base_interval * attempt_multiplier * success_multiplier

        # Implement the delay
        current_time = time.time()
        if hasattr(self, '_last_request_time'):
            time_since_last = current_time - self._last_request_time
            if time_since_last < adaptive_interval:
                delay = adaptive_interval - time_since_last
                self.logger.debug(
                    f"Adaptive rate limiting: waiting {delay:.2f}s")
                await asyncio.sleep(delay)

        self._last_request_time = time.time()

    async def _calculate_adaptive_backoff(self, attempt: int, status_code: int) -> float:
        """Calculate adaptive backoff delay based on error type and attempt number."""
        base_delay = 2.0

        # Status code specific delays
        status_multipliers = {
            429: 3.0,  # Rate limit - longer delay
            502: 2.0,  # Bad gateway - moderate delay
            503: 2.5,  # Service unavailable - longer delay
            500: 1.5,  # Server error - shorter delay
        }

        status_multiplier = status_multipliers.get(status_code, 1.0)

        # Exponential backoff with jitter
        exponential_delay = base_delay * (2 ** (attempt - 1))
        jittered_delay = exponential_delay * \
            (0.5 + (hash(str(time.time())) % 100) / 200)

        # Apply status multiplier
        final_delay = jittered_delay * status_multiplier

        # Cap maximum delay
        return min(final_delay, 30.0)

    async def _analyze_failure(self, status_code: int, error_message: str):
        """Analyze failure patterns for learning."""
        # Update error patterns
        error_key = f"{status_code}_{error_message[:50]}"
        self.metrics.error_patterns[error_key] = self.metrics.error_patterns.get(
            error_key, 0) + 1

        # Update success rate
        total_requests = sum(self.metrics.error_patterns.values())
        self.metrics.success_rate = max(0.0, 1.0 - (total_requests / (
            total_requests + self.method_performance['generate_response']['call_count'])))

        # Adjust strategy weights based on failure
        if status_code >= 500:
            self.adaptive_strategy.evolve_strategy('conservative_backoff', 0.2)
        elif status_code == 429:
            self.adaptive_strategy.evolve_strategy('intelligent_caching', 0.3)

        # Log failure analysis
        self.logger.warning(
            f"Request failed with status {status_code}. Pattern: {error_key}. "
            f"Current success rate: {self.metrics.success_rate:.2f}"
        )

    async def _continuous_improvement_loop(self):
        """Continuous self-improvement loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes

                # Analyze current performance
                bottlenecks = self.evolution_engine.analyze_bottlenecks()

                # Generate and apply optimizations
                for bottleneck in bottlenecks['slow_methods']:
                    if optimization_code := self.evolution_engine.generate_optimization_code(bottleneck):
                        self.evolution_engine.apply_evolution(
                            optimization_code, bottleneck['method'])

                # Take performance snapshot
                self._take_learning_snapshot()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Improvement loop error: {e}")
                await asyncio.sleep(60)  # Shorter retry interval on error

    def _take_learning_snapshot(self):
        """Take a snapshot of current learning state."""
        snapshot = LearningSnapshot(
            timestamp=datetime.now(),
            optimization_rules=self.evolution_engine.generated_methods.copy(),
            performance_deltas={
                method: metrics['avg_time']
                for method, metrics in self.method_performance.items()
            },
            success_patterns={
                k: v for k, v in self.metrics.error_patterns.items() if v < 3},
            failure_patterns={
                k: v for k, v in self.metrics.error_patterns.items() if v >= 3},
            adaptation_strategies=list(
                self.adaptive_strategy.strategies.keys())
        )
        self.learning_snapshots.append(snapshot)

        # Trim old snapshots
        if len(self.learning_snapshots) > 50:
            self.learning_snapshots = self.learning_snapshots[-50:]

    async def _save_learning_state(self):
        """Save learning state for future sessions."""
        try:
            state = {
                'metrics': self.metrics,
                'method_performance': dict(self.method_performance),
                'error_patterns': dict(self.metrics.error_patterns),
                'generated_methods': self.evolution_engine.generated_methods,
                # Save last 10 snapshots
                'learning_snapshots': self.learning_snapshots[-10:],
                'timestamp': datetime.now().isoformat()
            }

            # Save to file with timestamp
            save_path = Path('learning_state.pkl')
            with open(save_path, 'wb') as f:
                pickle.dump(state, f)

            self.logger.info(f"Learning state saved to {save_path}")

        except Exception as e:
            self.logger.error(f"Failed to save learning state: {e}")
