"""
Database manager for storing context, learning data, and configurations.
"""

import logging
import sqlite3
import aiosqlite
from pathlib import Path
from typing import Dict, Any


class DatabaseManager:
    """Manages database operations for the assistant."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the database manager."""
        self.config = config
        self.db_path = Path(config['path'])
        self.logger = logging.getLogger(__name__)

        self._connection = None

        self.logger.info("Database manager initialized")

    async def initialize(self):
        """Initialize the database and create tables."""
        # Ensure data directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Create tables
        await self._create_tables()

        self.logger.info("Database initialized")

    async def _create_tables(self):
        """Create necessary database tables."""
        async with aiosqlite.connect(self.db_path) as db:
            # Context table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS context (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    message TEXT NOT NULL,
                    is_assistant BOOLEAN NOT NULL,
                    timestamp TEXT NOT NULL,
                    metadata TEXT
                )
            ''')

            # Sessions table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    metadata TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    start_time TEXT,
                    end_time TEXT
                )
            ''')

            # Context summaries table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS context_summaries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    summary_text TEXT NOT NULL,
                    original_message_count INTEGER NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            ''')

            # Learning data table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS learning_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_input TEXT NOT NULL,
                    intent TEXT NOT NULL,
                    actions TEXT NOT NULL,
                    success BOOLEAN NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')

            # API performance metrics table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS api_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    endpoint TEXT NOT NULL,
                    method TEXT NOT NULL,
                    response_time REAL NOT NULL,
                    status_code INTEGER,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    timestamp TEXT NOT NULL,
                    model_used TEXT,
                    tokens_used INTEGER
                )
            ''')

            # Configuration history table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS config_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_section TEXT NOT NULL,
                    config_key TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT NOT NULL,
                    changed_by TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')

            await db.commit()

    async def close(self):
        """Close database connections."""
        if self._connection:
            await self._connection.close()
            self._connection = None

        self.logger.info("Database connections closed")

    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self.db_path.exists()
