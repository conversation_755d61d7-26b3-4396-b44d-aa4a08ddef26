"""
Database manager for storing context, learning data, and configurations.
"""

import logging
import sqlite3
import aiosqlite
from pathlib import Path
from typing import Dict, Any


class DatabaseManager:
    """Manages database operations for the assistant."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the database manager."""
        self.config = config
        self.db_path = Path(config['path'])
        self.logger = logging.getLogger(__name__)
        
        self._connection = None
        
        self.logger.info("Database manager initialized")
    
    async def initialize(self):
        """Initialize the database and create tables."""
        # Ensure data directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create tables
        await self._create_tables()
        
        self.logger.info("Database initialized")
    
    async def _create_tables(self):
        """Create necessary database tables."""
        async with aiosqlite.connect(self.db_path) as db:
            # Context table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS context (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    message TEXT NOT NULL,
                    is_assistant BOOLEAN NOT NULL,
                    timestamp TEXT NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # Sessions table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    start_time TEXT NOT NULL,
                    end_time TEXT
                )
            ''')
            
            # Learning data table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS learning_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_input TEXT NOT NULL,
                    intent TEXT NOT NULL,
                    actions TEXT NOT NULL,
                    success BOOLEAN NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            await db.commit()
    
    async def close(self):
        """Close database connections."""
        if self._connection:
            await self._connection.close()
            self._connection = None
        
        self.logger.info("Database connections closed")
    
    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self.db_path.exists()
