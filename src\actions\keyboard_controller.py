"""
Keyboard automation controller.
"""

import asyncio
import logging
import time
from typing import Dict, Any
import pyautogui
import keyboard


class KeyboardController:
    """Handles keyboard automation tasks."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the keyboard controller."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = config.get('keyboard_delay', 0.1)
        
        self.logger.info("Keyboard controller initialized")
    
    async def execute(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a keyboard command.
        
        Args:
            command: The keyboard command to execute
            parameters: Command parameters
            
        Returns:
            Result dictionary
        """
        try:
            command = command.lower()
            
            if command == 'type':
                return await self._type_text(parameters)
            elif command == 'key':
                return await self._press_key(parameters)
            elif command == 'hotkey':
                return await self._press_hotkey(parameters)
            elif command == 'hold':
                return await self._hold_key(parameters)
            elif command == 'release':
                return await self._release_key(parameters)
            else:
                return {
                    'success': False,
                    'error': f'Unknown keyboard command: {command}'
                }
                
        except Exception as e:
            self.logger.error(f"Keyboard command error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _type_text(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Type text."""
        text = parameters.get('text', '')
        interval = parameters.get('interval', 0.0)
        
        if not text:
            return {
                'success': False,
                'error': 'No text provided'
            }
        
        # Run in thread to avoid blocking
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.typewrite(text, interval=interval)
        )
        
        return {
            'success': True,
            'message': f'Typed text: {text[:50]}{"..." if len(text) > 50 else ""}'
        }
    
    async def _press_key(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Press a single key."""
        key = parameters.get('key', '')
        presses = parameters.get('presses', 1)
        interval = parameters.get('interval', 0.0)
        
        if not key:
            return {
                'success': False,
                'error': 'No key specified'
            }
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.press(key, presses=presses, interval=interval)
        )
        
        return {
            'success': True,
            'message': f'Pressed key: {key} ({presses} times)'
        }
    
    async def _press_hotkey(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Press a key combination."""
        keys = parameters.get('keys', [])
        
        if not keys:
            return {
                'success': False,
                'error': 'No keys specified for hotkey'
            }
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.hotkey(*keys)
        )
        
        return {
            'success': True,
            'message': f'Pressed hotkey: {"+".join(keys)}'
        }
    
    async def _hold_key(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Hold down a key."""
        key = parameters.get('key', '')
        
        if not key:
            return {
                'success': False,
                'error': 'No key specified'
            }
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.keyDown(key)
        )
        
        return {
            'success': True,
            'message': f'Holding key: {key}'
        }
    
    async def _release_key(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Release a held key."""
        key = parameters.get('key', '')
        
        if not key:
            return {
                'success': False,
                'error': 'No key specified'
            }
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.keyUp(key)
        )
        
        return {
            'success': True,
            'message': f'Released key: {key}'
        }
