"""
Voice interface for speech recognition and text-to-speech.
"""

import logging
from typing import Dict, Any, Optional
import speech_recognition as sr
import pyttsx3
import threading


class VoiceInterface:
    """Handles voice input and output."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the voice interface."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Initialize text-to-speech
        self.tts_engine = pyttsx3.init()
        self._configure_tts()
        
        # Calibrate microphone
        self._calibrate_microphone()

        # For background listening
        self.listening_thread: Optional[threading.Thread] = None
        self.stop_listening_event = threading.Event()
        
        self.logger.info("Voice interface initialized")
    
    def _configure_tts(self):
        """Configure text-to-speech settings."""
        rate = self.config.get('tts_rate', 200)
        volume = self.config.get('tts_volume', 0.8)
        
        self.tts_engine.setProperty('rate', rate)
        self.tts_engine.setProperty('volume', volume)
    
    def _calibrate_microphone(self):
        """Calibrate microphone for ambient noise."""
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            self.logger.info("Microphone calibrated")
        except Exception as e:
            self.logger.warning(f"Microphone calibration failed: {e}")
    
    def listen(self, continuous_mode: bool = False) -> Optional[str]:
        """Listen for voice input and return transcribed text."""
        try:
            with self.microphone as source:
                self.logger.debug("Listening for voice input...")
                
                # Adjust parameters for continuous mode
                phrase_timeout = None if continuous_mode else self.config.get('stt_phrase_timeout', 1)
                
                # Listen for audio
                audio = self.recognizer.listen(
                    source,
                    timeout=self.config.get('stt_timeout', 5),
                    phrase_time_limit=phrase_timeout
                )
                
                # Recognize speech
                text = self.recognizer.recognize_google(audio)
                self.logger.info(f"Recognized speech: {text}")
                return text
                
        except sr.WaitTimeoutError:
            self.logger.debug("Voice input timeout")
            return None
        except sr.UnknownValueError:
            self.logger.debug("Could not understand audio")
            return None
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Voice input error: {e}")
            return None
    
    def speak(self, text: str):
        """Convert text to speech and play it."""
        try:
            self.logger.debug(f"Speaking: {text[:50]}...")
            
            # Run TTS in a separate thread to avoid blocking
            def _speak():
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            
            thread = threading.Thread(target=_speak, daemon=True)
            thread.start()
            
        except Exception as e:
            self.logger.error(f"Text-to-speech error: {e}")

    def _background_listen_loop(self, 
                                on_speech_recognized_callback: callable,
                                on_listening_error_callback: callable):
        """Internal loop for background listening."""
        self.logger.info("Background listening loop started.")
        while not self.stop_listening_event.is_set():
            try:
                text = self.listen(continuous_mode=True)
                if text:
                    on_speech_recognized_callback(text)
                elif self.stop_listening_event.is_set(): # Check if stop was requested during listen
                    break
            except Exception as e:
                self.logger.error(f"Error in background listening loop: {e}")
                on_listening_error_callback(e)
                # Optionally, add a small delay before retrying to prevent rapid error loops
                # self.stop_listening_event.wait(0.5) # Example delay
        self.logger.info("Background listening loop stopped.")

    def start_background_listening(self, 
                                   on_speech_recognized_callback: callable, 
                                   on_listening_error_callback: callable):
        """Starts listening in a background thread."""
        if self.listening_thread and self.listening_thread.is_alive():
            self.logger.warning("Background listening is already active.")
            return

        self.stop_listening_event.clear()
        self.listening_thread = threading.Thread(
            target=self._background_listen_loop, 
            args=(on_speech_recognized_callback, on_listening_error_callback),
            daemon=True
        )
        self.listening_thread.start()
        self.logger.info("Background listening thread started.")

    def stop_background_listening(self):
        """Stops the background listening thread."""
        if self.listening_thread and self.listening_thread.is_alive():
            self.stop_listening_event.set()
            # self.listening_thread.join(timeout=2) # Wait for thread to finish
            # if self.listening_thread.is_alive():
            #     self.logger.warning("Background listening thread did not stop in time.")
            self.logger.info("Stop signal sent to background listening thread.")
        else:
            self.logger.info("Background listening is not active or thread already stopped.")
        self.listening_thread = None # Clear the thread reference
    
    def is_available(self) -> bool:
        """Check if voice interface is available."""
        try:
            # Test microphone
            with self.microphone as source:
                pass
            
            # Test TTS
            self.tts_engine.getProperty('rate')
            
            return True
            
        except Exception:
            return False
