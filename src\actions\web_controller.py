"""
Web automation controller.
"""

import asyncio
import logging
import webbrowser
from typing import Dict, Any


class WebController:
    """Handles web browser automation tasks."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the web controller."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("Web controller initialized")
    
    async def execute(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a web automation command."""
        try:
            command = command.lower()
            
            if command == 'open_url':
                return await self._open_url(parameters)
            elif command == 'search':
                return await self._search(parameters)
            else:
                return {
                    'success': False,
                    'error': f'Unknown web command: {command}'
                }
                
        except Exception as e:
            self.logger.error(f"Web command error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _open_url(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Open a URL in the default browser."""
        url = parameters.get('url', '')
        
        if not url:
            return {
                'success': False,
                'error': 'No URL specified'
            }
        
        try:
            webbrowser.open(url)
            
            return {
                'success': True,
                'message': f'Opened URL: {url}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to open URL: {e}'
            }
    
    async def _search(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Perform a web search."""
        query = parameters.get('query', '')
        engine = parameters.get('engine', 'google')
        
        if not query:
            return {
                'success': False,
                'error': 'No search query specified'
            }
        
        try:
            search_urls = {
                'google': f'https://www.google.com/search?q={query}',
                'bing': f'https://www.bing.com/search?q={query}',
                'duckduckgo': f'https://duckduckgo.com/?q={query}'
            }
            
            url = search_urls.get(engine, search_urls['google'])
            webbrowser.open(url)
            
            return {
                'success': True,
                'message': f'Searched for "{query}" using {engine}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to perform search: {e}'
            }
