"""
Performance monitoring module for DeepSeek API integration.
"""

import asyncio
import logging
import time
import json
import aiosqlite
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque


@dataclass
class APIMetrics:
    """API performance metrics."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    error_rate: float = 0.0
    success_rate: float = 0.0
    tokens_used: int = 0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class PerformanceAlert:
    """Performance alert data."""
    alert_type: str
    message: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    timestamp: datetime
    metrics: Dict[str, Any]


class PerformanceMonitor:
    """Monitors and tracks API performance metrics."""

    def __init__(self, db_manager, config: Dict[str, Any]):
        """Initialize performance monitor."""
        self.db_manager = db_manager
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Performance thresholds
        self.response_time_threshold = config.get('response_time_threshold', 5.0)
        self.error_rate_threshold = config.get('error_rate_threshold', 0.1)
        self.success_rate_threshold = config.get('success_rate_threshold', 0.9)
        
        # Metrics storage
        self.current_metrics = APIMetrics()
        self.recent_requests = deque(maxlen=1000)  # Last 1000 requests
        self.hourly_metrics = defaultdict(lambda: APIMetrics())
        self.daily_metrics = defaultdict(lambda: APIMetrics())
        
        # Alert system
        self.alerts = deque(maxlen=100)  # Last 100 alerts
        self.alert_cooldown = {}  # Prevent spam alerts
        
        # Performance trends
        self.performance_trends = {
            'response_times': deque(maxlen=100),
            'success_rates': deque(maxlen=100),
            'error_rates': deque(maxlen=100)
        }
        
        self.logger.info("Performance monitor initialized")

    async def record_api_call(
        self,
        endpoint: str,
        method: str,
        response_time: float,
        status_code: Optional[int],
        success: bool,
        error_message: Optional[str] = None,
        model_used: Optional[str] = None,
        tokens_used: Optional[int] = None
    ):
        """Record an API call for performance tracking."""
        timestamp = datetime.now()
        
        # Update current metrics
        self.current_metrics.total_requests += 1
        self.current_metrics.total_response_time += response_time
        
        if success:
            self.current_metrics.successful_requests += 1
        else:
            self.current_metrics.failed_requests += 1
        
        if tokens_used:
            self.current_metrics.tokens_used += tokens_used
        
        # Update response time statistics
        self.current_metrics.min_response_time = min(
            self.current_metrics.min_response_time, response_time
        )
        self.current_metrics.max_response_time = max(
            self.current_metrics.max_response_time, response_time
        )
        
        # Calculate rates
        if self.current_metrics.total_requests > 0:
            self.current_metrics.avg_response_time = (
                self.current_metrics.total_response_time / 
                self.current_metrics.total_requests
            )
            self.current_metrics.success_rate = (
                self.current_metrics.successful_requests / 
                self.current_metrics.total_requests
            )
            self.current_metrics.error_rate = (
                self.current_metrics.failed_requests / 
                self.current_metrics.total_requests
            )
        
        # Store request details
        request_data = {
            'endpoint': endpoint,
            'method': method,
            'response_time': response_time,
            'status_code': status_code,
            'success': success,
            'error_message': error_message,
            'model_used': model_used,
            'tokens_used': tokens_used,
            'timestamp': timestamp
        }
        
        self.recent_requests.append(request_data)
        
        # Update hourly and daily metrics
        hour_key = timestamp.strftime('%Y-%m-%d-%H')
        day_key = timestamp.strftime('%Y-%m-%d')
        
        self._update_period_metrics(self.hourly_metrics[hour_key], request_data)
        self._update_period_metrics(self.daily_metrics[day_key], request_data)
        
        # Store in database
        await self._store_metrics_in_db(request_data)
        
        # Check for performance issues
        await self._check_performance_alerts()
        
        # Update trends
        self._update_performance_trends()

    def _update_period_metrics(self, metrics: APIMetrics, request_data: Dict[str, Any]):
        """Update metrics for a specific time period."""
        metrics.total_requests += 1
        metrics.total_response_time += request_data['response_time']
        
        if request_data['success']:
            metrics.successful_requests += 1
        else:
            metrics.failed_requests += 1
        
        if request_data['tokens_used']:
            metrics.tokens_used += request_data['tokens_used']
        
        # Update response time statistics
        metrics.min_response_time = min(
            metrics.min_response_time, request_data['response_time']
        )
        metrics.max_response_time = max(
            metrics.max_response_time, request_data['response_time']
        )
        
        # Calculate rates
        if metrics.total_requests > 0:
            metrics.avg_response_time = (
                metrics.total_response_time / metrics.total_requests
            )
            metrics.success_rate = (
                metrics.successful_requests / metrics.total_requests
            )
            metrics.error_rate = (
                metrics.failed_requests / metrics.total_requests
            )
        
        metrics.last_updated = datetime.now()

    async def _store_metrics_in_db(self, request_data: Dict[str, Any]):
        """Store metrics in database."""
        try:
            async with aiosqlite.connect(self.db_manager.db_path) as db:
                await db.execute(
                    """
                    INSERT INTO api_metrics 
                    (endpoint, method, response_time, status_code, success, 
                     error_message, timestamp, model_used, tokens_used)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        request_data['endpoint'],
                        request_data['method'],
                        request_data['response_time'],
                        request_data['status_code'],
                        request_data['success'],
                        request_data['error_message'],
                        request_data['timestamp'].isoformat(),
                        request_data['model_used'],
                        request_data['tokens_used']
                    )
                )
                await db.commit()
        except Exception as e:
            self.logger.error(f"Failed to store metrics in database: {e}")

    async def _check_performance_alerts(self):
        """Check for performance issues and generate alerts."""
        current_time = datetime.now()
        
        # Check response time threshold
        if (self.current_metrics.avg_response_time > self.response_time_threshold and
            self._should_send_alert('high_response_time', current_time)):
            
            alert = PerformanceAlert(
                alert_type='high_response_time',
                message=f"Average response time ({self.current_metrics.avg_response_time:.2f}s) "
                       f"exceeds threshold ({self.response_time_threshold}s)",
                severity='medium',
                timestamp=current_time,
                metrics={'avg_response_time': self.current_metrics.avg_response_time}
            )
            await self._send_alert(alert)
        
        # Check error rate threshold
        if (self.current_metrics.error_rate > self.error_rate_threshold and
            self._should_send_alert('high_error_rate', current_time)):
            
            alert = PerformanceAlert(
                alert_type='high_error_rate',
                message=f"Error rate ({self.current_metrics.error_rate:.2%}) "
                       f"exceeds threshold ({self.error_rate_threshold:.2%})",
                severity='high',
                timestamp=current_time,
                metrics={'error_rate': self.current_metrics.error_rate}
            )
            await self._send_alert(alert)
        
        # Check success rate threshold
        if (self.current_metrics.success_rate < self.success_rate_threshold and
            self.current_metrics.total_requests > 10 and
            self._should_send_alert('low_success_rate', current_time)):
            
            alert = PerformanceAlert(
                alert_type='low_success_rate',
                message=f"Success rate ({self.current_metrics.success_rate:.2%}) "
                       f"below threshold ({self.success_rate_threshold:.2%})",
                severity='high',
                timestamp=current_time,
                metrics={'success_rate': self.current_metrics.success_rate}
            )
            await self._send_alert(alert)

    def _should_send_alert(self, alert_type: str, current_time: datetime) -> bool:
        """Check if alert should be sent (considering cooldown)."""
        cooldown_minutes = 15  # 15 minute cooldown
        
        if alert_type in self.alert_cooldown:
            last_alert_time = self.alert_cooldown[alert_type]
            if current_time - last_alert_time < timedelta(minutes=cooldown_minutes):
                return False
        
        return True

    async def _send_alert(self, alert: PerformanceAlert):
        """Send performance alert."""
        self.alerts.append(alert)
        self.alert_cooldown[alert.alert_type] = alert.timestamp
        
        # Log the alert
        self.logger.warning(f"Performance Alert [{alert.severity.upper()}]: {alert.message}")
        
        # Here you could add additional alert mechanisms:
        # - Send email notifications
        # - Post to Slack/Discord
        # - Write to monitoring systems
        # - Trigger automated responses

    def _update_performance_trends(self):
        """Update performance trend data."""
        if self.current_metrics.total_requests > 0:
            self.performance_trends['response_times'].append(
                self.current_metrics.avg_response_time
            )
            self.performance_trends['success_rates'].append(
                self.current_metrics.success_rate
            )
            self.performance_trends['error_rates'].append(
                self.current_metrics.error_rate
            )

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return {
            'total_requests': self.current_metrics.total_requests,
            'successful_requests': self.current_metrics.successful_requests,
            'failed_requests': self.current_metrics.failed_requests,
            'avg_response_time': self.current_metrics.avg_response_time,
            'min_response_time': self.current_metrics.min_response_time,
            'max_response_time': self.current_metrics.max_response_time,
            'success_rate': self.current_metrics.success_rate,
            'error_rate': self.current_metrics.error_rate,
            'tokens_used': self.current_metrics.tokens_used,
            'last_updated': self.current_metrics.last_updated.isoformat()
        }

    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent performance alerts."""
        recent_alerts = list(self.alerts)[-limit:]
        return [
            {
                'alert_type': alert.alert_type,
                'message': alert.message,
                'severity': alert.severity,
                'timestamp': alert.timestamp.isoformat(),
                'metrics': alert.metrics
            }
            for alert in recent_alerts
        ]

    async def get_historical_metrics(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get historical metrics from database."""
        try:
            async with aiosqlite.connect(self.db_manager.db_path) as db:
                cursor = await db.execute(
                    """
                    SELECT endpoint, method, response_time, status_code, success,
                           error_message, timestamp, model_used, tokens_used
                    FROM api_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                    """,
                    (start_date.isoformat(), end_date.isoformat())
                )
                
                rows = await cursor.fetchall()
                return [
                    {
                        'endpoint': row[0],
                        'method': row[1],
                        'response_time': row[2],
                        'status_code': row[3],
                        'success': bool(row[4]),
                        'error_message': row[5],
                        'timestamp': row[6],
                        'model_used': row[7],
                        'tokens_used': row[8]
                    }
                    for row in rows
                ]
        except Exception as e:
            self.logger.error(f"Failed to retrieve historical metrics: {e}")
            return []

    def reset_metrics(self):
        """Reset current metrics (useful for testing or periodic resets)."""
        self.current_metrics = APIMetrics()
        self.recent_requests.clear()
        self.logger.info("Performance metrics reset")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        return {
            'current_metrics': self.get_current_metrics(),
            'recent_alerts': self.get_recent_alerts(5),
            'trends': {
                'response_time_trend': list(self.performance_trends['response_times'])[-10:],
                'success_rate_trend': list(self.performance_trends['success_rates'])[-10:],
                'error_rate_trend': list(self.performance_trends['error_rates'])[-10:]
            },
            'thresholds': {
                'response_time_threshold': self.response_time_threshold,
                'error_rate_threshold': self.error_rate_threshold,
                'success_rate_threshold': self.success_rate_threshold
            }
        }
