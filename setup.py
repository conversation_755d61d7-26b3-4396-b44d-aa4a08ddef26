#!/usr/bin/env python3
"""
Setup script for the AI Assistant project.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def create_directories():
    """Create necessary directories."""
    directories = [
        'data',
        'logs',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")


def setup_config():
    """Setup configuration file."""
    config_path = Path('config/config.json')
    example_path = Path('config/config.example.json')
    
    if not config_path.exists() and example_path.exists():
        # Copy example config
        with open(example_path, 'r') as f:
            config = json.load(f)
        
        # Prompt for API key
        api_key = input("Enter your OpenRouter API key (or press Enter to skip): ").strip()
        if api_key:
            config['openrouter']['api_key'] = api_key
        
        # Save config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✓ Created configuration file: {config_path}")
    else:
        print(f"✓ Configuration file already exists: {config_path}")


def install_dependencies():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing dependencies: {e}")
        return False
    
    return True


def run_tests():
    """Run the test suite."""
    print("Running tests...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pytest', 'tests/', '-v'
        ])
        print("✓ All tests passed")
    except subprocess.CalledProcessError as e:
        print(f"✗ Some tests failed: {e}")
        return False
    
    return True


def main():
    """Main setup function."""
    print("🤖 AI Assistant Setup")
    print("=" * 50)
    
    # Create directories
    print("\n1. Creating directories...")
    create_directories()
    
    # Install dependencies
    print("\n2. Installing dependencies...")
    if not install_dependencies():
        print("Setup failed. Please check the error messages above.")
        sys.exit(1)
    
    # Setup configuration
    print("\n3. Setting up configuration...")
    setup_config()
    
    # Run tests
    print("\n4. Running tests...")
    if not run_tests():
        print("⚠️  Some tests failed, but setup is complete.")
        print("You can still run the assistant, but some features may not work correctly.")
    
    print("\n" + "=" * 50)
    print("🎉 Setup complete!")
    print("\nTo start the assistant:")
    print("  python main.py")
    print("\nTo run tests:")
    print("  pytest tests/")
    print("\nFor more information, see docs/DEVELOPMENT.md")


if __name__ == '__main__':
    main()
