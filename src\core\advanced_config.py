"""
Advanced configuration management with validation, hot-reloading, and environment support.
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
import aiofiles
import aiofiles.os


@dataclass
class APIConfig:
    """API configuration structure."""
    api_key: str
    base_url: str
    model: str
    timeout: float = 30.0
    max_retries: int = 3
    min_request_interval: float = 0.5
    temperature: float = 0.3
    max_tokens: int = 300
    enable_caching: bool = True
    enable_self_improvement: bool = True
    performance_monitoring: bool = True


@dataclass
class FallbackConfig:
    """Fallback configuration structure."""
    enabled: bool = True
    max_failures_before_fallback: int = 3
    custom_responses: Dict[str, List[str]] = None
    enable_pattern_matching: bool = True
    enable_local_calculations: bool = True


@dataclass
class PerformanceConfig:
    """Performance monitoring configuration."""
    enabled: bool = True
    response_time_threshold: float = 5.0
    error_rate_threshold: float = 0.1
    success_rate_threshold: float = 0.9
    alert_cooldown_minutes: int = 15
    metrics_retention_days: int = 30


class AdvancedConfigManager:
    """Advanced configuration manager with validation and hot-reloading."""

    def __init__(self, config_path: str = "config/config.json"):
        """Initialize advanced config manager."""
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        
        # Configuration cache
        self._config_cache = {}
        self._last_modified = None
        
        # Validation rules
        self._validation_rules = {
            'deepseek.api_key': self._validate_api_key,
            'deepseek.base_url': self._validate_url,
            'deepseek.timeout': lambda x: isinstance(x, (int, float)) and x > 0,
            'deepseek.max_retries': lambda x: isinstance(x, int) and 0 <= x <= 10,
            'deepseek.temperature': lambda x: isinstance(x, (int, float)) and 0 <= x <= 2,
            'deepseek.max_tokens': lambda x: isinstance(x, int) and 1 <= x <= 4000,
        }
        
        # Environment variable mappings
        self._env_mappings = {
            'DEEPSEEK_API_KEY': 'deepseek.api_key',
            'DEEPSEEK_BASE_URL': 'deepseek.base_url',
            'DEEPSEEK_MODEL': 'deepseek.model',
            'PERFORMANCE_MONITORING': 'deepseek.performance_monitoring',
        }
        
        # Configuration watchers
        self._watchers = []
        self._watch_task = None
        
        self.logger.info("Advanced config manager initialized")

    async def load_config(self, force_reload: bool = False) -> Dict[str, Any]:
        """Load configuration with caching and validation."""
        try:
            # Check if we need to reload
            if not force_reload and self._config_cache and not await self._config_changed():
                return self._config_cache.copy()
            
            # Load from file
            if await aiofiles.os.path.exists(self.config_path):
                async with aiofiles.open(self.config_path, 'r') as f:
                    content = await f.read()
                    config = json.loads(content)
            else:
                self.logger.warning(f"Config file not found: {self.config_path}")
                config = self._get_default_config()
            
            # Apply environment variable overrides
            config = self._apply_env_overrides(config)
            
            # Validate configuration
            validation_errors = await self._validate_config(config)
            if validation_errors:
                self.logger.error(f"Configuration validation errors: {validation_errors}")
                # Use defaults for invalid values
                config = self._fix_invalid_config(config, validation_errors)
            
            # Cache the configuration
            self._config_cache = config.copy()
            self._last_modified = await self._get_file_mtime()
            
            self.logger.info("Configuration loaded and validated successfully")
            return config
            
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            # Return default configuration as fallback
            return self._get_default_config()

    async def save_config(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file."""
        try:
            # Validate before saving
            validation_errors = await self._validate_config(config)
            if validation_errors:
                self.logger.error(f"Cannot save invalid configuration: {validation_errors}")
                return False
            
            # Ensure directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file
            async with aiofiles.open(self.config_path, 'w') as f:
                await f.write(json.dumps(config, indent=2))
            
            # Update cache
            self._config_cache = config.copy()
            self._last_modified = await self._get_file_mtime()
            
            # Notify watchers
            await self._notify_watchers(config)
            
            self.logger.info("Configuration saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False

    async def get_api_config(self) -> APIConfig:
        """Get structured API configuration."""
        config = await self.load_config()
        deepseek_config = config.get('deepseek', {})
        
        return APIConfig(
            api_key=deepseek_config.get('api_key', ''),
            base_url=deepseek_config.get('base_url', 'https://api.deepseek.com/v1'),
            model=deepseek_config.get('model', 'deepseek-chat'),
            timeout=deepseek_config.get('timeout', 30.0),
            max_retries=deepseek_config.get('max_retries', 3),
            min_request_interval=deepseek_config.get('min_request_interval', 0.5),
            temperature=deepseek_config.get('temperature', 0.3),
            max_tokens=deepseek_config.get('max_tokens', 300),
            enable_caching=deepseek_config.get('enable_caching', True),
            enable_self_improvement=deepseek_config.get('enable_self_improvement', True),
            performance_monitoring=deepseek_config.get('performance_monitoring', True)
        )

    async def get_fallback_config(self) -> FallbackConfig:
        """Get structured fallback configuration."""
        config = await self.load_config()
        fallback_config = config.get('fallback', {})
        
        return FallbackConfig(
            enabled=fallback_config.get('enabled', True),
            max_failures_before_fallback=fallback_config.get('max_failures_before_fallback', 3),
            custom_responses=fallback_config.get('custom_responses'),
            enable_pattern_matching=fallback_config.get('enable_pattern_matching', True),
            enable_local_calculations=fallback_config.get('enable_local_calculations', True)
        )

    async def get_performance_config(self) -> PerformanceConfig:
        """Get structured performance configuration."""
        config = await self.load_config()
        perf_config = config.get('performance', {})
        
        return PerformanceConfig(
            enabled=perf_config.get('enabled', True),
            response_time_threshold=perf_config.get('response_time_threshold', 5.0),
            error_rate_threshold=perf_config.get('error_rate_threshold', 0.1),
            success_rate_threshold=perf_config.get('success_rate_threshold', 0.9),
            alert_cooldown_minutes=perf_config.get('alert_cooldown_minutes', 15),
            metrics_retention_days=perf_config.get('metrics_retention_days', 30)
        )

    async def start_watching(self):
        """Start watching configuration file for changes."""
        if self._watch_task:
            return
        
        self._watch_task = asyncio.create_task(self._watch_config_file())
        self.logger.info("Started configuration file watching")

    async def stop_watching(self):
        """Stop watching configuration file."""
        if self._watch_task:
            self._watch_task.cancel()
            try:
                await self._watch_task
            except asyncio.CancelledError:
                pass
            self._watch_task = None
        
        self.logger.info("Stopped configuration file watching")

    def add_watcher(self, callback):
        """Add a configuration change watcher."""
        self._watchers.append(callback)

    def remove_watcher(self, callback):
        """Remove a configuration change watcher."""
        if callback in self._watchers:
            self._watchers.remove(callback)

    async def _watch_config_file(self):
        """Watch configuration file for changes."""
        while True:
            try:
                await asyncio.sleep(1)  # Check every second
                
                if await self._config_changed():
                    self.logger.info("Configuration file changed, reloading...")
                    new_config = await self.load_config(force_reload=True)
                    await self._notify_watchers(new_config)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error watching config file: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _config_changed(self) -> bool:
        """Check if configuration file has changed."""
        try:
            current_mtime = await self._get_file_mtime()
            return current_mtime != self._last_modified
        except Exception:
            return False

    async def _get_file_mtime(self) -> Optional[float]:
        """Get file modification time."""
        try:
            stat = await aiofiles.os.stat(self.config_path)
            return stat.st_mtime
        except Exception:
            return None

    async def _notify_watchers(self, config: Dict[str, Any]):
        """Notify all watchers of configuration changes."""
        for watcher in self._watchers:
            try:
                if asyncio.iscoroutinefunction(watcher):
                    await watcher(config)
                else:
                    watcher(config)
            except Exception as e:
                self.logger.error(f"Error notifying config watcher: {e}")

    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides."""
        for env_var, config_path in self._env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Parse the config path and set the value
                keys = config_path.split('.')
                current = config
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # Convert value to appropriate type
                if env_value.lower() in ('true', 'false'):
                    env_value = env_value.lower() == 'true'
                elif env_value.isdigit():
                    env_value = int(env_value)
                elif env_value.replace('.', '').isdigit():
                    env_value = float(env_value)
                
                current[keys[-1]] = env_value
                self.logger.info(f"Applied environment override: {env_var} -> {config_path}")
        
        return config

    async def _validate_config(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration against rules."""
        errors = []
        
        for config_path, validator in self._validation_rules.items():
            try:
                keys = config_path.split('.')
                value = config
                for key in keys:
                    value = value.get(key)
                    if value is None:
                        break
                
                if value is not None and not validator(value):
                    errors.append(f"Invalid value for {config_path}: {value}")
                    
            except Exception as e:
                errors.append(f"Error validating {config_path}: {e}")
        
        return errors

    def _validate_api_key(self, api_key: str) -> bool:
        """Validate API key format."""
        if not isinstance(api_key, str):
            return False
        return len(api_key) > 10 and (api_key.startswith('sk-') or api_key.startswith('sk-or-'))

    def _validate_url(self, url: str) -> bool:
        """Validate URL format."""
        if not isinstance(url, str):
            return False
        return url.startswith(('http://', 'https://'))

    def _fix_invalid_config(self, config: Dict[str, Any], errors: List[str]) -> Dict[str, Any]:
        """Fix invalid configuration values with defaults."""
        defaults = self._get_default_config()
        
        for error in errors:
            if "deepseek.api_key" in error:
                config.setdefault('deepseek', {})['api_key'] = defaults['deepseek']['api_key']
            elif "deepseek.base_url" in error:
                config.setdefault('deepseek', {})['base_url'] = defaults['deepseek']['base_url']
            # Add more fixes as needed
        
        return config

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "deepseek": {
                "api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "base_url": "https://api.deepseek.com/v1",
                "model": "deepseek-chat",
                "timeout": 30.0,
                "max_retries": 3,
                "min_request_interval": 0.5,
                "temperature": 0.3,
                "max_tokens": 300,
                "enable_caching": True,
                "enable_self_improvement": True,
                "performance_monitoring": True
            },
            "fallback": {
                "enabled": True,
                "max_failures_before_fallback": 3,
                "enable_pattern_matching": True,
                "enable_local_calculations": True
            },
            "performance": {
                "enabled": True,
                "response_time_threshold": 5.0,
                "error_rate_threshold": 0.1,
                "success_rate_threshold": 0.9,
                "alert_cooldown_minutes": 15,
                "metrics_retention_days": 30
            },
            "assistant": {
                "name": "Assistant",
                "wake_word": "hey assistant",
                "voice_enabled": True,
                "auto_execute": False,
                "confirmation_required": True
            },
            "database": {
                "path": "data/assistant.db",
                "backup_enabled": True,
                "backup_interval_hours": 24
            },
            "logging": {
                "level": "INFO",
                "file": "logs/assistant.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
