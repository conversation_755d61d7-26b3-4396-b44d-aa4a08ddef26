{"deepseek": {"api_key": "YOUR_DEEPSEEK_API_KEY_HERE", "base_url": "https://api.deepseek.com/v1", "model": "deepseek-chat", "timeout": 30.0, "max_retries": 3, "min_request_interval": 0.5, "temperature": 0.3, "max_tokens": 300, "enable_caching": true, "enable_self_improvement": true, "performance_monitoring": true}, "assistant": {"name": "Assistant", "wake_word": "hey assistant", "voice_enabled": true, "auto_execute": false, "confirmation_required": true}, "voice": {"tts_engine": "pyttsx3", "tts_rate": 200, "tts_volume": 0.8, "stt_timeout": 5, "stt_phrase_timeout": 1}, "automation": {"mouse_speed": 0.5, "keyboard_delay": 0.1, "screenshot_quality": 80, "safety_checks": true}, "learning": {"enabled": true, "feedback_collection": true, "auto_improvement": true, "context_retention_days": 30}, "database": {"path": "data/assistant.db", "backup_enabled": true, "backup_interval_hours": 24}, "logging": {"level": "INFO", "file": "logs/assistant.log", "max_size_mb": 10, "backup_count": 5}}