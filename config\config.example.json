{"deepseek": {"api_key": "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", "base_url": "https://openrouter.ai/api/v1", "model": "deepseek/deepseek-r1:free"}, "assistant": {"name": "Assistant", "wake_word": "hey assistant", "voice_enabled": true, "auto_execute": false, "confirmation_required": true}, "voice": {"tts_engine": "pyttsx3", "tts_rate": 200, "tts_volume": 0.8, "stt_timeout": 5, "stt_phrase_timeout": 1}, "automation": {"mouse_speed": 0.5, "keyboard_delay": 0.1, "screenshot_quality": 80, "safety_checks": true}, "learning": {"enabled": true, "feedback_collection": true, "auto_improvement": true, "context_retention_days": 30}, "database": {"path": "data/assistant.db", "backup_enabled": true, "backup_interval_hours": 24}, "logging": {"level": "INFO", "file": "logs/assistant.log", "max_size_mb": 10, "backup_count": 5}}