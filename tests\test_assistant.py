"""
Tests for the main AI Assistant functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from src.core.assistant import AI<PERSON>sistant


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'openrouter': {
            'api_key': 'test_key',
            'base_url': 'https://test.api',
            'model': 'test_model'
        },
        'database': {
            'path': ':memory:'
        },
        'automation': {
            'safety_checks': True
        },
        'learning': {
            'enabled': True
        }
    }


@pytest.fixture
async def assistant(mock_config):
    """Create an assistant instance for testing."""
    assistant = AIAssistant(mock_config)
    await assistant.start()
    yield assistant
    await assistant.stop()


@pytest.mark.asyncio
async def test_assistant_initialization(mock_config):
    """Test assistant initialization."""
    assistant = AIAssistant(mock_config)
    assert assistant.config == mock_config
    assert not assistant.is_running
    assert assistant.current_session_id is None


@pytest.mark.asyncio
async def test_assistant_start_stop(assistant):
    """Test assistant start and stop functionality."""
    assert assistant.is_running
    assert assistant.current_session_id is not None
    
    await assistant.stop()
    assert not assistant.is_running


@pytest.mark.asyncio
async def test_process_input(assistant):
    """Test input processing."""
    with patch.object(assistant.intent_manager, 'analyze_intent') as mock_analyze:
        mock_analyze.return_value = {
            'response': 'Test response',
            'actions': [],
            'confidence': 0.9,
            'requires_confirmation': False
        }
        
        result = await assistant.process_input("test input", "text")
        
        assert result['response'] == 'Test response'
        assert result['confidence'] == 0.9
        assert not result['requires_confirmation']


@pytest.mark.asyncio
async def test_session_management(assistant):
    """Test session ID generation and management."""
    session_id = assistant.current_session_id
    assert session_id is not None
    assert session_id.startswith('session_')
    
    status = assistant.get_status()
    assert status['running'] == True
    assert status['session_id'] == session_id


if __name__ == '__main__':
    pytest.main([__file__])
