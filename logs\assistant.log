2025-05-28 19:54:01,563 - root - INFO - Logging configured successfully
2025-05-28 19:54:01,564 - __main__ - INFO - Starting AI Assistant...
2025-05-28 19:54:01,564 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 19:54:01,565 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 19:54:01,565 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 19:54:01,566 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 19:54:01,566 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 19:54:01,567 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 19:54:01,567 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 19:54:01,568 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 19:54:01,568 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 19:54:01,568 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 19:54:01,569 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 19:54:01,569 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 19:54:02,742 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 19:54:02,743 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 19:54:04,194 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 19:54:04,194 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 19:54:04,195 - src.ui.interface - INFO - User interface initialized
2025-05-28 19:54:04,195 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 19:54:04,196 - src.ui.interface - INFO - Starting user interface...
2025-05-28 19:54:04,656 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_195404
2025-05-28 19:54:04,671 - src.data.database_manager - INFO - Database initialized
2025-05-28 19:54:04,672 - src.core.learning_module - INFO - Learning module started
2025-05-28 19:54:05,008 - src.ui.interface - INFO - GUI created successfully
2025-05-28 19:54:12,553 - src.ui.voice_interface - INFO - Recognized speech: hello
2025-05-28 19:54:12,557 - src.core.assistant - INFO - Processing voice input: hello
2025-05-28 19:54:12,557 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: 'NoneType' object has no attribute 'post'
2025-05-28 19:54:20,904 - src.ui.voice_interface - INFO - Recognized speech: what's the problem
2025-05-28 19:54:20,909 - src.core.assistant - INFO - Processing voice input: what's the problem
2025-05-28 19:54:20,910 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: 'NoneType' object has no attribute 'post'
2025-05-28 19:54:36,658 - src.ui.voice_interface - INFO - Recognized speech: hold on bro
2025-05-28 19:54:36,662 - src.core.assistant - INFO - Processing voice input: hold on bro
2025-05-28 19:54:36,662 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: 'NoneType' object has no attribute 'post'
2025-05-28 19:54:45,721 - root - INFO - Logging configured successfully
2025-05-28 19:54:45,722 - __main__ - INFO - Starting AI Assistant...
2025-05-28 19:54:45,722 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 19:54:45,723 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 19:54:45,723 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 19:54:45,723 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 19:54:45,724 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 19:54:45,724 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 19:54:45,724 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 19:54:45,725 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 19:54:45,725 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 19:54:45,726 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 19:54:45,726 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 19:54:45,726 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 19:54:45,942 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 19:54:45,943 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 19:54:47,170 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 19:54:47,170 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 19:54:47,171 - src.ui.interface - INFO - User interface initialized
2025-05-28 19:54:47,171 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 19:54:47,171 - src.ui.interface - INFO - Starting user interface...
2025-05-28 19:54:47,238 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_195447
2025-05-28 19:54:47,242 - src.data.database_manager - INFO - Database initialized
2025-05-28 19:54:47,243 - src.core.learning_module - INFO - Learning module started
2025-05-28 19:54:47,542 - src.ui.interface - INFO - GUI created successfully
2025-05-28 19:54:52,426 - src.ui.voice_interface - INFO - Recognized speech: what's good my niggaa
2025-05-28 19:54:52,430 - src.core.assistant - INFO - Processing voice input: what's good my niggaa
2025-05-28 19:54:52,956 - src.ai.openrouter_client - ERROR - OpenRouter API error 402: {"error":{"message":"Insufficient credits. Add more using https://openrouter.ai/settings/credits","code":402}}
2025-05-28 19:55:05,235 - src.core.assistant - INFO - Processing text input: y
2025-05-28 19:55:05,235 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: Timeout context manager should be used inside a task
2025-05-28 19:58:33,323 - root - INFO - Logging configured successfully
2025-05-28 19:58:33,324 - __main__ - INFO - Starting AI Assistant...
2025-05-28 19:58:33,324 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 19:58:33,325 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 19:58:33,325 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 19:58:33,325 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 19:58:33,325 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 19:58:33,326 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 19:58:33,326 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 19:58:33,327 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 19:58:33,327 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 19:58:33,327 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 19:58:33,327 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 19:58:33,328 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 19:58:33,548 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 19:58:33,548 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 19:58:34,780 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 19:58:34,780 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 19:58:34,781 - src.ui.interface - INFO - User interface initialized
2025-05-28 19:58:34,781 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 19:58:34,781 - src.ui.interface - INFO - Starting user interface...
2025-05-28 19:58:34,848 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_195834
2025-05-28 19:58:34,851 - src.data.database_manager - INFO - Database initialized
2025-05-28 19:58:34,852 - src.core.learning_module - INFO - Learning module started
2025-05-28 19:58:35,159 - src.ui.interface - INFO - GUI created successfully
2025-05-28 19:58:39,392 - src.ui.voice_interface - INFO - Recognized speech: what's good brother
2025-05-28 19:58:39,396 - src.core.assistant - INFO - Processing voice input: what's good brother
2025-05-28 19:58:39,814 - src.ai.openrouter_client - ERROR - OpenRouter API error 402: {"error":{"message":"Insufficient credits. Add more using https://openrouter.ai/settings/credits","code":402}}
2025-05-28 20:01:33,099 - src.core.assistant - INFO - Processing text input: status
2025-05-28 20:01:38,260 - src.core.assistant - INFO - Processing text input: hello
2025-05-28 20:01:45,084 - src.ui.voice_interface - INFO - Recognized speech: can you hear
2025-05-28 20:01:45,088 - src.core.assistant - INFO - Processing voice input: can you hear
2025-05-28 20:01:45,089 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: Timeout context manager should be used inside a task
2025-05-28 20:09:40,872 - src.ui.voice_interface - INFO - Recognized speech: can you hear me
2025-05-28 20:09:40,876 - src.core.assistant - INFO - Processing voice input: can you hear me
2025-05-28 20:09:40,877 - src.ai.openrouter_client - ERROR - Error calling OpenRouter API: Timeout context manager should be used inside a task
2025-05-28 20:09:56,809 - src.ui.interface - INFO - Shutting down assistant...
2025-05-28 20:09:56,810 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 20:09:56,811 - src.core.context_processor - INFO - Saving session context: session_20250528_195834
2025-05-28 20:09:56,811 - src.core.learning_module - INFO - Learning module stopped
2025-05-28 20:09:56,811 - src.data.database_manager - INFO - Database connections closed
2025-05-28 20:09:56,812 - src.core.assistant - INFO - Assistant stopped
2025-05-28 20:09:56,824 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001449995BE10>
2025-05-28 20:09:56,825 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001449966CAD0>, 7427.609)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001449995B010>
2025-05-28 20:09:56,863 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] The handle is invalid
2025-05-28 20:12:59,134 - root - INFO - Logging configured successfully
2025-05-28 20:12:59,135 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:12:59,135 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:12:59,136 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:12:59,136 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:12:59,137 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:12:59,137 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:12:59,138 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:12:59,138 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:12:59,139 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:12:59,139 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:12:59,140 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:12:59,140 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:12:59,141 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:12:59,424 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:12:59,426 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:13:00,687 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:13:00,687 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:13:00,688 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:13:00,688 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:13:00,689 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:13:00,790 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_201300
2025-05-28 20:13:00,795 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:13:00,796 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:13:01,173 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:19:44,824 - __main__ - INFO - Assistant stopped by user
2025-05-28 20:21:52,729 - root - INFO - Logging configured successfully
2025-05-28 20:21:52,730 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:21:52,731 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:21:52,731 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:21:52,732 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:21:52,733 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:21:52,734 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:21:52,735 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:21:52,735 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:21:52,736 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:21:52,736 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:21:52,737 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:21:52,737 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:21:52,737 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:21:53,069 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:21:53,070 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:21:54,401 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:21:54,401 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:21:54,402 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:21:54,402 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:21:54,402 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:21:54,512 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_202154
2025-05-28 20:21:54,517 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:21:54,517 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:21:54,997 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:22:20,480 - src.core.assistant - INFO - Processing text input: hi
2025-05-28 20:22:54,022 - src.ui.voice_interface - INFO - Recognized speech: can you hear
2025-05-28 20:22:54,027 - src.core.assistant - INFO - Processing voice input: can you hear
2025-05-28 20:22:55,437 - src.ai.deepseek_client - ERROR - DeepSeek API error 402: {"error":{"message":"Insufficient Balance","type":"unknown_error","param":null,"code":"invalid_request_error"}}
2025-05-28 20:23:25,916 - src.ui.voice_interface - INFO - Background listening loop started.
2025-05-28 20:23:25,916 - src.ui.voice_interface - INFO - Background listening thread started.
2025-05-28 20:23:25,916 - src.ui.interface - INFO - Continuous listening started by user.
2025-05-28 20:23:31,378 - src.ui.voice_interface - INFO - Recognized speech: can you
2025-05-28 20:23:31,382 - src.ui.interface - INFO - Handling recognized speech: can you
2025-05-28 20:23:31,384 - src.core.assistant - INFO - Processing voice input: can you
2025-05-28 20:23:31,385 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 20:23:52,084 - src.ui.voice_interface - INFO - Recognized speech: what would help you process the request if I get
2025-05-28 20:23:52,087 - src.ui.interface - INFO - Handling recognized speech: what would help you process the request if I get
2025-05-28 20:23:52,089 - src.core.assistant - INFO - Processing voice input: what would help you process the request if I get
2025-05-28 20:23:52,090 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 20:24:50,650 - src.core.assistant - INFO - Processing text input: can u hear
2025-05-28 20:24:50,651 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 20:24:53,865 - src.core.assistant - INFO - Processing text input: ehy
2025-05-28 20:24:53,866 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 20:25:03,111 - src.ui.interface - INFO - Closing application...
2025-05-28 20:25:03,111 - src.ui.voice_interface - INFO - Stop signal sent to background listening thread.
2025-05-28 20:25:03,112 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 20:25:03,113 - src.core.context_processor - INFO - Saving session context: session_20250528_202154
2025-05-28 20:25:03,113 - src.core.learning_module - INFO - Learning module stopped
2025-05-28 20:25:03,113 - src.data.database_manager - INFO - Database connections closed
2025-05-28 20:25:03,114 - src.core.assistant - INFO - Assistant stopped
2025-05-28 20:33:02,805 - root - INFO - Logging configured successfully
2025-05-28 20:33:02,806 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:33:02,807 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:33:02,807 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:33:02,808 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:33:02,808 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:33:02,808 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:33:02,809 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:33:02,809 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:33:02,810 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:33:02,810 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:33:02,810 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:33:02,811 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:33:02,811 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:33:03,035 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:33:03,036 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:33:04,273 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:33:04,274 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:33:04,274 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:33:04,274 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:33:04,275 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:33:04,355 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_203304
2025-05-28 20:33:04,360 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:33:04,361 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:33:04,692 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:33:32,945 - src.core.assistant - INFO - Processing text input: hello
2025-05-28 20:33:32,947 - src.core.assistant - ERROR - Error processing input: 'DatabaseManager' object has no attribute '_get_connection'
2025-05-28 20:36:07,808 - __main__ - INFO - Assistant stopped by user
2025-05-28 20:37:10,294 - root - INFO - Logging configured successfully
2025-05-28 20:37:10,294 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:37:10,295 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:37:10,296 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:37:10,296 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:37:10,297 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:37:10,297 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:37:10,297 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:37:10,298 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:37:10,298 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:37:10,299 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:37:10,299 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:37:10,300 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:37:10,300 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:37:10,512 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:37:10,513 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:37:11,765 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:37:11,766 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:37:11,766 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:37:11,767 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:37:11,767 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:37:11,833 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_203711
2025-05-28 20:37:11,836 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:37:11,836 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:37:12,139 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:37:27,385 - src.core.assistant - INFO - Processing text input: hi
2025-05-28 20:37:27,387 - src.core.assistant - ERROR - Error processing input: no such table: sessions
2025-05-28 20:37:34,179 - src.core.assistant - INFO - Processing text input: ho
2025-05-28 20:37:34,182 - src.core.assistant - ERROR - Error processing input: table context has no column named metadata
2025-05-28 20:37:36,347 - src.ui.voice_interface - INFO - Background listening loop started.
2025-05-28 20:37:36,348 - src.ui.voice_interface - INFO - Background listening thread started.
2025-05-28 20:37:36,348 - src.ui.interface - INFO - Continuous listening started by user.
2025-05-28 20:37:45,488 - src.ui.voice_interface - INFO - Recognized speech: cloudy can you hear
2025-05-28 20:37:45,494 - src.ui.interface - INFO - Handling recognized speech: cloudy can you hear
2025-05-28 20:37:45,496 - src.core.assistant - INFO - Processing voice input: cloudy can you hear
2025-05-28 20:37:45,500 - src.core.assistant - ERROR - Error processing input: table context has no column named metadata
2025-05-28 20:40:03,958 - src.ui.voice_interface - INFO - Recognized speech: hey Google
2025-05-28 20:40:03,963 - src.ui.interface - INFO - Handling recognized speech: hey Google
2025-05-28 20:40:03,964 - src.core.assistant - INFO - Processing voice input: hey Google
2025-05-28 20:40:03,968 - src.core.assistant - ERROR - Error processing input: table context has no column named metadata
2025-05-28 20:40:58,070 - src.ui.voice_interface - INFO - Recognized speech: I think it does
2025-05-28 20:40:58,073 - src.ui.interface - INFO - Handling recognized speech: I think it does
2025-05-28 20:40:58,074 - src.core.assistant - INFO - Processing voice input: I think it does
2025-05-28 20:40:58,078 - src.core.assistant - ERROR - Error processing input: table context has no column named metadata
2025-05-28 20:44:25,963 - root - INFO - Logging configured successfully
2025-05-28 20:44:25,963 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:44:25,964 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:44:25,965 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:44:25,965 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:44:25,965 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:44:25,966 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:44:25,966 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:44:25,966 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:44:25,967 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:44:25,967 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:44:25,967 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:44:25,968 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:44:25,968 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:44:26,203 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:44:26,204 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:44:27,475 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:44:27,476 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:44:27,476 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:44:27,477 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:44:27,477 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:44:27,541 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_204427
2025-05-28 20:44:27,552 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:44:27,553 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:44:27,865 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:44:33,471 - src.ui.voice_interface - INFO - Recognized speech: whiskey
2025-05-28 20:44:33,476 - src.core.assistant - INFO - Processing voice input: whiskey
2025-05-28 20:44:33,480 - src.core.assistant - ERROR - Error processing input: no such table: sessions
2025-05-28 20:44:42,623 - src.core.assistant - INFO - Processing text input: why
2025-05-28 20:44:42,631 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free
2025-05-28 20:45:41,590 - src.core.assistant - INFO - Processing text input: k
2025-05-28 20:45:41,596 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free
2025-05-28 20:45:41,597 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 20:45:50,213 - src.ui.interface - INFO - Closing application...
2025-05-28 20:45:50,214 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 20:45:50,215 - src.core.context_processor - INFO - Saving session context: session_20250528_204427
2025-05-28 20:45:50,218 - src.ui.interface - ERROR - Error stopping assistant: no such table: sessions
2025-05-28 20:45:50,266 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002845644EB90>
2025-05-28 20:45:50,267 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000028456151470>, 10193.781)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000028454E9AE90>
2025-05-28 20:46:55,476 - root - INFO - Logging configured successfully
2025-05-28 20:46:55,477 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:46:55,478 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:46:55,479 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:46:55,479 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:46:55,479 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:46:55,480 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:46:55,480 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:46:55,481 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:46:55,481 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:46:55,481 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:46:55,482 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:46:55,482 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:46:55,482 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:46:55,710 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:46:55,711 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:46:56,952 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:46:56,953 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:46:56,953 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:46:56,954 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:46:56,954 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:46:57,023 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_204657
2025-05-28 20:46:57,037 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:46:57,038 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:46:57,358 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:47:07,453 - src.ui.voice_interface - INFO - Recognized speech: is it
2025-05-28 20:47:07,457 - src.core.assistant - INFO - Processing voice input: is it
2025-05-28 20:47:07,468 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free
2025-05-28 20:48:20,892 - src.ui.interface - INFO - Closing application...
2025-05-28 20:48:20,895 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 20:48:20,895 - src.core.context_processor - INFO - Saving session context: session_20250528_204657
2025-05-28 20:48:20,898 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 20:48:52,641 - root - INFO - Logging configured successfully
2025-05-28 20:48:52,642 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:48:52,642 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:48:52,643 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:48:52,644 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:48:52,644 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:48:52,645 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:48:52,645 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:48:52,646 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:48:52,646 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:48:52,646 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:48:52,647 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:48:52,647 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:48:52,647 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:48:52,889 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:48:52,890 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:48:54,136 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:48:54,136 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:48:54,137 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:48:54,137 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:48:54,137 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:48:54,202 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_204854
2025-05-28 20:48:54,206 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:48:54,206 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:48:54,507 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:49:00,372 - src.ui.voice_interface - INFO - Recognized speech: call Lucky
2025-05-28 20:49:00,377 - src.core.assistant - INFO - Processing voice input: call Lucky
2025-05-28 20:49:00,385 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free
2025-05-28 20:49:30,387 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: 
2025-05-28 20:50:56,600 - root - INFO - Logging configured successfully
2025-05-28 20:50:56,601 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:50:56,602 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:50:56,602 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:50:56,603 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:50:56,603 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:50:56,604 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:50:56,604 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:50:56,605 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:50:56,605 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:50:56,606 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:50:56,606 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:50:56,607 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:50:56,607 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:50:56,854 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:50:56,854 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:50:58,103 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:50:58,103 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:50:58,104 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:50:58,104 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:50:58,104 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:50:58,173 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_205058
2025-05-28 20:50:58,177 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:50:58,178 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:50:58,487 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:51:02,820 - src.ui.voice_interface - INFO - Recognized speech: can you hear me today
2025-05-28 20:51:02,825 - src.core.assistant - INFO - Processing voice input: can you hear me today
2025-05-28 20:51:02,833 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free
2025-05-28 20:54:05,089 - root - INFO - Logging configured successfully
2025-05-28 20:54:05,090 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:54:05,091 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:54:05,091 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:54:05,092 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:54:05,093 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:54:05,094 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:54:05,094 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:54:05,095 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:54:05,095 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:54:05,096 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:54:05,096 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:54:05,096 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:54:05,097 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:54:05,345 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:54:05,346 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:54:06,626 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:54:06,627 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:54:06,628 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:54:06,628 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:54:06,629 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:54:06,695 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_205406
2025-05-28 20:54:06,698 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:54:06,699 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:54:07,015 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:54:11,937 - src.core.assistant - INFO - Processing text input: hi
2025-05-28 20:54:16,050 - src.core.assistant - INFO - Processing text input: api
2025-05-28 20:54:16,057 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/3)
2025-05-28 20:56:47,678 - root - INFO - Logging configured successfully
2025-05-28 20:56:47,678 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:56:47,679 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:56:47,680 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:56:47,680 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:56:47,681 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:56:47,681 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:56:47,682 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:56:47,682 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:56:47,683 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:56:47,683 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:56:47,683 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:56:47,684 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:56:47,684 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:56:47,946 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:56:47,947 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:56:49,221 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:56:49,222 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:56:49,223 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:56:49,223 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:56:49,223 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:56:49,296 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_205649
2025-05-28 20:56:49,301 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:56:49,301 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:56:49,730 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:56:56,909 - src.ui.voice_interface - INFO - Recognized speech: can you
2025-05-28 20:56:56,915 - src.core.assistant - INFO - Processing voice input: can you
2025-05-28 20:56:56,923 - src.core.intent_manager - ERROR - Error analyzing intent: object NoneType can't be used in 'await' expression
2025-05-28 20:57:02,718 - src.ui.voice_interface - INFO - Recognized speech: Do you have a
2025-05-28 20:57:02,723 - src.core.assistant - INFO - Processing voice input: Do you have a
2025-05-28 20:57:02,731 - src.core.intent_manager - ERROR - Error analyzing intent: object NoneType can't be used in 'await' expression
2025-05-28 20:57:08,433 - src.ui.voice_interface - INFO - Recognized speech: what is the
2025-05-28 20:57:08,439 - src.core.assistant - INFO - Processing voice input: what is the
2025-05-28 20:57:08,447 - src.core.intent_manager - ERROR - Error analyzing intent: object NoneType can't be used in 'await' expression
2025-05-28 20:58:22,989 - root - INFO - Logging configured successfully
2025-05-28 20:58:22,989 - __main__ - INFO - Starting AI Assistant...
2025-05-28 20:58:22,990 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 20:58:22,990 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 20:58:22,991 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 20:58:22,991 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 20:58:22,992 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 20:58:22,993 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 20:58:22,993 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 20:58:22,994 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 20:58:22,994 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 20:58:22,994 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 20:58:22,995 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 20:58:22,995 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 20:58:23,231 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 20:58:23,231 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 20:58:24,494 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 20:58:24,494 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 20:58:24,495 - src.ui.interface - INFO - User interface initialized
2025-05-28 20:58:24,495 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 20:58:24,495 - src.ui.interface - INFO - Starting user interface...
2025-05-28 20:58:24,560 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_205824
2025-05-28 20:58:24,565 - src.data.database_manager - INFO - Database initialized
2025-05-28 20:58:24,565 - src.core.learning_module - INFO - Learning module started
2025-05-28 20:58:24,881 - src.ui.interface - INFO - GUI created successfully
2025-05-28 20:59:08,424 - src.ui.voice_interface - INFO - Recognized speech: can you hear me now
2025-05-28 20:59:08,428 - src.core.assistant - INFO - Processing voice input: can you hear me now
2025-05-28 20:59:08,437 - src.core.intent_manager - ERROR - Error analyzing intent: object NoneType can't be used in 'await' expression
2025-05-28 20:59:11,134 - src.ui.interface - INFO - Closing application...
2025-05-28 20:59:11,136 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 20:59:11,136 - src.core.context_processor - INFO - Saving session context: session_20250528_205824
2025-05-28 20:59:11,140 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 20:59:11,183 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000195ADB29C10>
2025-05-28 21:02:22,441 - root - INFO - Logging configured successfully
2025-05-28 21:02:22,442 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:02:22,442 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:02:22,443 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:02:22,443 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:02:22,444 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:02:22,444 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:02:22,445 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:02:22,445 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:02:22,446 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:02:22,446 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:02:22,446 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:02:22,447 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:02:22,447 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:02:22,673 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:02:22,674 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:02:23,930 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:02:23,931 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:02:23,931 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:02:23,931 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:02:23,933 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:02:24,001 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_210224
2025-05-28 21:02:24,005 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:02:24,006 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:02:24,328 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:02:30,456 - src.ui.voice_interface - INFO - Recognized speech: how do
2025-05-28 21:02:30,461 - src.core.assistant - INFO - Processing voice input: how do
2025-05-28 21:02:30,470 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/4)
2025-05-28 21:02:42,994 - src.ui.voice_interface - INFO - Recognized speech: what the fuc
2025-05-28 21:02:42,998 - src.core.assistant - INFO - Processing voice input: what the fuc
2025-05-28 21:02:43,005 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/4)
2025-05-28 21:02:43,006 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:02:45,022 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 2/4)
2025-05-28 21:02:45,023 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:02:47,029 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 3/4)
2025-05-28 21:02:47,030 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:02:49,041 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 4/4)
2025-05-28 21:02:49,041 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:02:50,048 - src.ai.deepseek_client - ERROR - Failed to get response after 4 attempts. Last error: Unexpected error: Timeout context manager should be used inside a task
2025-05-28 21:02:58,233 - src.ui.voice_interface - INFO - Recognized speech: too many
2025-05-28 21:02:58,240 - src.core.assistant - INFO - Processing voice input: too many
2025-05-28 21:02:58,247 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/4)
2025-05-28 21:02:58,249 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:03:00,264 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 2/4)
2025-05-28 21:03:00,265 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:03:02,260 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 3/4)
2025-05-28 21:03:02,261 - src.ai.deepseek_client - ERROR - Error calling DeepSeek API: Timeout context manager should be used inside a task
2025-05-28 21:03:07,939 - root - INFO - Logging configured successfully
2025-05-28 21:03:07,940 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:03:07,941 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:03:07,941 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:03:07,942 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:03:07,942 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:03:07,943 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:03:07,943 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:03:07,944 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:03:07,944 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:03:07,944 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:03:07,945 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:03:07,945 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:03:07,945 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:03:08,176 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:03:08,177 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:03:09,423 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:03:09,424 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:03:09,425 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:03:09,425 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:03:09,425 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:03:09,490 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_210309
2025-05-28 21:03:09,495 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:03:09,495 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:03:09,810 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:03:14,543 - src.ui.voice_interface - INFO - Recognized speech: can you work now
2025-05-28 21:03:14,547 - src.core.assistant - INFO - Processing voice input: can you work now
2025-05-28 21:03:14,555 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/4)
2025-05-28 21:05:01,002 - root - INFO - Logging configured successfully
2025-05-28 21:05:01,003 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:05:01,003 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:05:01,004 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:05:01,005 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:05:01,005 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:05:01,005 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:05:01,006 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:05:01,006 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:05:01,007 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:05:01,007 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:05:01,007 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:05:01,008 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:05:01,009 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:05:01,281 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:05:01,282 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:05:02,566 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:05:02,567 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:05:02,568 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:05:02,568 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:05:02,568 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:05:02,644 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_210502
2025-05-28 21:05:02,654 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:05:02,654 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:05:02,994 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:05:44,729 - src.ui.voice_interface - INFO - Recognized speech: Claudia
2025-05-28 21:05:44,733 - src.core.assistant - INFO - Processing voice input: Claudia
2025-05-28 21:05:44,741 - src.ai.deepseek_client - INFO - Sending request to https://openrouter.ai/api/v1 with model: deepseek/deepseek-r1:free (Attempt 1/4)
2025-05-28 21:11:29,867 - root - INFO - Logging configured successfully
2025-05-28 21:11:29,867 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:11:29,868 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:11:29,869 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:11:29,870 - __main__ - ERROR - Fatal error: 'DeepSeekClient' object has no attribute '_aggressive_retry_strategy'
2025-05-28 21:11:51,836 - root - INFO - Logging configured successfully
2025-05-28 21:11:51,837 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:11:51,838 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:11:51,838 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:11:51,840 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:11:51,840 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:11:51,841 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:11:51,841 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:11:51,842 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:11:51,842 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:11:51,843 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:11:51,843 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:11:51,844 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:11:51,844 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:11:52,093 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:11:52,094 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:11:53,345 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:11:53,346 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:11:53,346 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:11:53,347 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:11:53,347 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:11:53,419 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211153
2025-05-28 21:11:53,423 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:11:53,424 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:11:53,761 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:11:58,603 - src.ui.voice_interface - INFO - Recognized speech: can you hear me now
2025-05-28 21:11:58,608 - src.core.assistant - INFO - Processing voice input: can you hear me now
2025-05-28 21:11:58,615 - src.core.intent_manager - ERROR - Error analyzing intent: 'DeepSeekClient' object has no attribute 'analyze_intent'
2025-05-28 21:12:06,025 - src.ui.voice_interface - INFO - Recognized speech: yes it
2025-05-28 21:12:06,029 - src.core.assistant - INFO - Processing voice input: yes it
2025-05-28 21:12:06,036 - src.core.intent_manager - ERROR - Error analyzing intent: 'DeepSeekClient' object has no attribute 'analyze_intent'
2025-05-28 21:12:30,307 - root - INFO - Logging configured successfully
2025-05-28 21:12:30,308 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:12:30,309 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:12:30,310 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:12:30,311 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:12:30,312 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:12:30,313 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:12:30,314 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:12:30,314 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:12:30,315 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:12:30,315 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:12:30,316 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:12:30,316 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:12:30,316 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:12:30,548 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:12:30,549 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:12:31,791 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:12:31,792 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:12:31,793 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:12:31,793 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:12:31,794 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:12:31,859 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211231
2025-05-28 21:12:31,863 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:12:31,863 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:12:32,196 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:12:37,243 - src.ui.voice_interface - INFO - Recognized speech: can you hear me
2025-05-28 21:12:37,248 - src.core.assistant - INFO - Processing voice input: can you hear me
2025-05-28 21:12:37,255 - src.core.intent_manager - ERROR - Error analyzing intent: DeepSeekClient.analyze_intent() takes 2 positional arguments but 3 were given
2025-05-28 21:13:00,890 - root - INFO - Logging configured successfully
2025-05-28 21:13:00,891 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:13:00,891 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:13:00,892 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:13:00,893 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:13:00,894 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:13:00,895 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:13:00,895 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:13:00,896 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:13:00,896 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:13:00,897 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:13:00,897 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:13:00,898 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:13:00,898 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:13:01,125 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:13:01,126 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:13:02,373 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:13:02,374 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:13:02,375 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:13:02,375 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:13:02,375 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:13:02,441 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211302
2025-05-28 21:13:02,444 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:13:02,445 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:13:02,757 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:13:08,482 - src.ui.voice_interface - INFO - Recognized speech: can you hear a n
2025-05-28 21:13:08,486 - src.core.assistant - INFO - Processing voice input: can you hear a n
2025-05-28 21:13:08,493 - src.ai.deepseek_client.1950627078544 - INFO - API request attempt 1/6
2025-05-28 21:13:15,374 - src.ai.deepseek_client.1950627078544 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute '_update_success_metrics'
2025-05-28 21:13:15,375 - src.ai.deepseek_client.1950627078544 - INFO - Method generate_response completed in 6.882s
2025-05-28 21:13:15,376 - src.ai.deepseek_client.1950627078544 - ERROR - Error in analyze_intent: 'DeepSeekClient' object has no attribute '_handle_adaptive_error'
2025-05-28 21:13:15,376 - src.core.assistant - ERROR - Error processing input: 'response'
2025-05-28 21:13:24,221 - src.ui.voice_interface - INFO - Recognized speech: are you fucking
2025-05-28 21:13:24,227 - src.core.assistant - INFO - Processing voice input: are you fucking
2025-05-28 21:13:24,234 - src.ai.deepseek_client.1950627078544 - INFO - API request attempt 1/6
2025-05-28 21:13:24,235 - src.ai.deepseek_client.1950627078544 - WARNING - Request attempt 1 failed: Timeout context manager should be used inside a task
2025-05-28 21:13:24,236 - src.ai.deepseek_client.1950627078544 - INFO - Method generate_response completed in 0.002s
2025-05-28 21:13:24,236 - src.ai.deepseek_client.1950627078544 - ERROR - Error in analyze_intent: 'DeepSeekClient' object has no attribute '_handle_adaptive_error'
2025-05-28 21:13:24,237 - src.core.assistant - ERROR - Error processing input: 'response'
2025-05-28 21:13:47,574 - root - INFO - Logging configured successfully
2025-05-28 21:13:47,575 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:13:47,576 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:13:47,576 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:13:47,579 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:13:47,580 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:13:47,580 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:13:47,580 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:13:47,581 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:13:47,581 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:13:47,581 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:13:47,582 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:13:47,582 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:13:47,583 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:13:47,868 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:13:47,869 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:13:49,166 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:13:49,166 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:13:49,167 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:13:49,167 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:13:49,167 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:13:49,255 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211349
2025-05-28 21:13:49,262 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:13:49,263 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:13:49,684 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:14:15,591 - src.core.assistant - INFO - Processing text input: kk
2025-05-28 21:14:15,599 - src.ai.deepseek_client.2110762459024 - INFO - API request attempt 1/6
2025-05-28 21:14:20,482 - src.ai.deepseek_client.2110762459024 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute '_update_success_metrics'
2025-05-28 21:14:20,483 - src.ai.deepseek_client.2110762459024 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute '_update_success_metrics'
2025-05-28 21:14:20,483 - src.ai.deepseek_client.2110762459024 - INFO - Method generate_response completed in 4.884s
2025-05-28 21:14:20,484 - src.ai.deepseek_client.2110762459024 - ERROR - Error in analyze_intent: 'DeepSeekClient' object has no attribute 'error_counts'
2025-05-28 21:14:20,485 - src.core.assistant - ERROR - Error processing input: 'response'
2025-05-28 21:15:34,892 - root - INFO - Logging configured successfully
2025-05-28 21:15:34,893 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:15:34,894 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:15:34,895 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:15:34,896 - __main__ - ERROR - Fatal error: 'DeepSeekClient' object has no attribute 'adaptive_strategy'
2025-05-28 21:15:56,527 - root - INFO - Logging configured successfully
2025-05-28 21:15:56,527 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:15:56,528 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:15:56,528 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:15:56,529 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:15:56,530 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:15:56,530 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:15:56,531 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:15:56,531 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:15:56,531 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:15:56,532 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:15:56,532 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:15:56,533 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:15:56,533 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:15:56,761 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:15:56,761 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:15:58,005 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:15:58,005 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:15:58,006 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:15:58,006 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:15:58,007 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:15:58,076 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211558
2025-05-28 21:15:58,081 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:15:58,081 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:15:58,433 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:16:02,583 - src.ui.voice_interface - INFO - Recognized speech: now did you
2025-05-28 21:16:02,588 - src.core.assistant - INFO - Processing voice input: now did you
2025-05-28 21:16:02,595 - src.core.intent_manager - ERROR - Error analyzing intent: 'DeepSeekClient' object has no attribute 'metrics'
2025-05-28 21:16:31,116 - root - INFO - Logging configured successfully
2025-05-28 21:16:31,117 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:16:31,118 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:16:31,118 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:16:31,119 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:16:31,119 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:16:31,120 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:16:31,120 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:16:31,120 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:16:31,121 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:16:31,121 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:16:31,121 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:16:31,122 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:16:31,123 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:16:31,359 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:16:31,360 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:16:32,609 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:16:32,610 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:16:32,610 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:16:32,611 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:16:32,611 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:16:32,680 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_211632
2025-05-28 21:16:32,685 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:16:32,686 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:16:33,007 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:16:37,466 - src.ui.voice_interface - INFO - Recognized speech: what about now
2025-05-28 21:16:37,471 - src.core.assistant - INFO - Processing voice input: what about now
2025-05-28 21:16:37,477 - src.core.intent_manager - ERROR - Error analyzing intent: 'DeepSeekClient' object has no attribute 'timeout_multiplier'
2025-05-28 21:16:42,443 - src.ui.interface - INFO - Closing application...
2025-05-28 21:16:42,445 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 21:16:42,445 - src.core.context_processor - INFO - Saving session context: session_20250528_211632
2025-05-28 21:16:42,448 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 21:21:52,650 - root - INFO - Logging configured successfully
2025-05-28 21:21:52,651 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:21:52,652 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:21:52,652 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:21:52,653 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:21:52,653 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:21:52,653 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:21:52,654 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:21:52,654 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:21:52,654 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:21:52,654 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:21:52,655 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:21:52,655 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:21:52,655 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:21:52,891 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:21:52,891 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:21:54,180 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:21:54,180 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:21:54,181 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:21:54,181 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:21:54,181 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:21:54,248 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212154
2025-05-28 21:21:54,252 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:21:54,252 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:21:54,578 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:22:12,323 - src.core.assistant - INFO - Processing text input: hh
2025-05-28 21:22:12,331 - src.ai.deepseek_client.1510661759952 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:22:12,332 - src.ai.deepseek_client.1510661759952 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:22:12,333 - src.ai.deepseek_client.1510661759952 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:22:12,333 - src.core.assistant - ERROR - Error processing input: 'response'
2025-05-28 21:22:38,311 - __main__ - INFO - Assistant stopped by user
2025-05-28 21:22:41,035 - root - INFO - Logging configured successfully
2025-05-28 21:22:41,035 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:22:41,036 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:22:41,037 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:22:41,037 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:22:41,037 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:22:41,038 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:22:41,038 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:22:41,038 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:22:41,039 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:22:41,039 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:22:41,039 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:22:41,040 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:22:41,040 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:22:41,252 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:22:41,253 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:22:42,491 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:22:42,491 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:22:42,492 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:22:42,492 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:22:42,492 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:22:42,554 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212242
2025-05-28 21:22:42,559 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:22:42,559 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:22:42,856 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:22:47,707 - src.ui.voice_interface - INFO - Recognized speech: hello
2025-05-28 21:22:47,711 - src.core.assistant - INFO - Processing voice input: hello
2025-05-28 21:22:53,949 - src.ui.voice_interface - INFO - Recognized speech: do you finally have
2025-05-28 21:22:53,953 - src.core.assistant - INFO - Processing voice input: do you finally have
2025-05-28 21:22:53,959 - src.ai.deepseek_client.3083376994384 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:22:53,960 - src.ai.deepseek_client.3083376994384 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:22:53,960 - src.ai.deepseek_client.3083376994384 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:22:53,961 - src.core.assistant - ERROR - Error processing input: 'response'
2025-05-28 21:22:57,959 - src.ui.interface - INFO - Closing application...
2025-05-28 21:22:57,960 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 21:22:57,962 - src.core.context_processor - INFO - Saving session context: session_20250528_212242
2025-05-28 21:22:57,964 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 21:22:58,017 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002CDEA2562D0>
2025-05-28 21:24:47,978 - root - INFO - Logging configured successfully
2025-05-28 21:24:47,979 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:24:47,979 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:24:47,980 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:24:47,981 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:24:47,981 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:24:47,981 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:24:47,982 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:24:47,982 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:24:47,983 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:24:47,983 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:24:47,984 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:24:47,984 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:24:47,984 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:24:48,195 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:24:48,196 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:24:49,454 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:24:49,454 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:24:49,455 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:24:49,455 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:24:49,456 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:24:49,521 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212449
2025-05-28 21:24:49,524 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:24:49,525 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:24:49,847 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:24:55,053 - src.ui.voice_interface - INFO - Recognized speech: do you have API
2025-05-28 21:24:55,057 - src.core.assistant - INFO - Processing voice input: do you have API
2025-05-28 21:24:55,063 - src.ai.deepseek_client.2040051541904 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:24:55,064 - src.ai.deepseek_client.2040051541904 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute 'min_request_interval'
2025-05-28 21:24:55,065 - src.ai.deepseek_client.2040051541904 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:25:31,566 - root - INFO - Logging configured successfully
2025-05-28 21:25:31,567 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:25:31,567 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:25:31,568 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:25:31,568 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:25:31,569 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:25:31,569 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:25:31,570 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:25:31,570 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:25:31,570 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:25:31,571 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:25:31,571 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:25:31,572 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:25:31,572 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:25:31,784 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:25:31,785 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:25:33,025 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:25:33,026 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:25:33,026 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:25:33,026 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:25:33,026 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:25:33,098 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212533
2025-05-28 21:25:33,105 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:25:33,105 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:25:33,417 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:25:40,220 - src.ui.voice_interface - INFO - Recognized speech: do you have API
2025-05-28 21:25:40,224 - src.core.assistant - INFO - Processing voice input: do you have API
2025-05-28 21:25:40,232 - src.ai.deepseek_client.2914858772368 - INFO - API request attempt 1/6
2025-05-28 21:25:42,864 - src.ai.deepseek_client.2914858772368 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute '_update_success_metrics'
2025-05-28 21:25:42,864 - src.ai.deepseek_client.2914858772368 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute '_update_success_metrics'
2025-05-28 21:25:42,865 - src.ai.deepseek_client.2914858772368 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:26:23,196 - root - INFO - Logging configured successfully
2025-05-28 21:26:23,196 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:26:23,197 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:26:23,198 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:26:23,200 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:26:23,201 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:26:23,201 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:26:23,201 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:26:23,202 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:26:23,203 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:26:23,203 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:26:23,204 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:26:23,206 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:26:23,206 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:26:23,476 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:26:23,477 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:26:24,751 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:26:24,753 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:26:24,753 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:26:24,754 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:26:24,754 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:26:24,827 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212624
2025-05-28 21:26:24,832 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:26:24,833 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:26:25,170 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:26:30,139 - src.ui.voice_interface - INFO - Recognized speech: do you have an AP
2025-05-28 21:26:30,144 - src.core.assistant - INFO - Processing voice input: do you have an AP
2025-05-28 21:26:30,153 - src.ai.deepseek_client.2359103925648 - INFO - API request attempt 1/6
2025-05-28 21:26:36,173 - src.ai.deepseek_client.2359103925648 - WARNING - Request attempt 1 failed: 'PerformanceMetrics' object has no attribute 'total_requests'
2025-05-28 21:26:36,173 - src.ai.deepseek_client.2359103925648 - ERROR - [Perf: N/A] - Error on attempt 1: 'PerformanceMetrics' object has no attribute 'total_requests'
2025-05-28 21:26:36,174 - src.ai.deepseek_client.2359103925648 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:27:47,779 - root - INFO - Logging configured successfully
2025-05-28 21:27:47,779 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:27:47,780 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:27:47,780 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:27:47,781 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:27:47,781 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:27:47,782 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:27:47,782 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:27:47,783 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:27:47,783 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:27:47,784 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:27:47,784 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:27:47,785 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:27:47,785 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:27:48,018 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:27:48,019 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:27:49,265 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:27:49,265 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:27:49,266 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:27:49,266 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:27:49,266 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:27:49,331 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_212749
2025-05-28 21:27:49,336 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:27:49,336 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:27:49,643 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:28:01,083 - src.ui.voice_interface - INFO - Recognized speech: APL
2025-05-28 21:28:01,088 - src.core.assistant - INFO - Processing voice input: APL
2025-05-28 21:28:01,097 - src.ai.deepseek_client.2167425085264 - INFO - API request attempt 1/6
2025-05-28 21:28:11,869 - src.ai.deepseek_client.2167425085264 - WARNING - Request attempt 1 failed: 'PerformanceMetrics' object has no attribute 'start_time'
2025-05-28 21:28:11,870 - src.ai.deepseek_client.2167425085264 - ERROR - [Perf: N/A] - Error on attempt 1: 'PerformanceMetrics' object has no attribute 'start_time'
2025-05-28 21:28:11,871 - src.ai.deepseek_client.2167425085264 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:28:27,716 - src.ui.interface - INFO - Closing application...
2025-05-28 21:28:27,718 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 21:28:27,718 - src.core.context_processor - INFO - Saving session context: session_20250528_212749
2025-05-28 21:28:27,722 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 21:28:27,772 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001F8A7088210>, 12799.671)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001F8A73564D0>
2025-05-28 21:54:47,932 - root - INFO - Logging configured successfully
2025-05-28 21:54:47,933 - __main__ - INFO - Starting AI Assistant...
2025-05-28 21:54:47,933 - src.core.config_manager - INFO - Configuration loaded successfully
2025-05-28 21:54:47,934 - src.data.database_manager - INFO - Database manager initialized
2025-05-28 21:54:47,935 - src.core.context_processor - INFO - Context processor initialized
2025-05-28 21:54:47,935 - src.core.intent_manager - INFO - Intent manager initialized
2025-05-28 21:54:47,936 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-05-28 21:54:47,937 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-05-28 21:54:47,937 - src.actions.system_controller - INFO - System controller initialized
2025-05-28 21:54:47,938 - src.actions.file_controller - INFO - File controller initialized
2025-05-28 21:54:47,938 - src.actions.web_controller - INFO - Web controller initialized
2025-05-28 21:54:47,938 - src.actions.action_executor - INFO - Action executor initialized
2025-05-28 21:54:47,939 - src.core.learning_module - INFO - Learning module initialized
2025-05-28 21:54:47,939 - src.core.assistant - INFO - AI Assistant initialized
2025-05-28 21:54:48,167 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-05-28 21:54:48,168 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-05-28 21:54:49,410 - src.ui.voice_interface - INFO - Microphone calibrated
2025-05-28 21:54:49,410 - src.ui.voice_interface - INFO - Voice interface initialized
2025-05-28 21:54:49,411 - src.ui.interface - INFO - User interface initialized
2025-05-28 21:54:49,411 - __main__ - INFO - AI Assistant initialized successfully
2025-05-28 21:54:49,411 - src.ui.interface - INFO - Starting user interface...
2025-05-28 21:54:49,477 - src.core.assistant - INFO - Assistant started with session ID: session_20250528_215449
2025-05-28 21:54:49,481 - src.data.database_manager - INFO - Database initialized
2025-05-28 21:54:49,481 - src.core.learning_module - INFO - Learning module started
2025-05-28 21:54:49,786 - src.ui.interface - INFO - GUI created successfully
2025-05-28 21:54:55,978 - src.ui.voice_interface - INFO - Recognized speech: if you have Appiah
2025-05-28 21:54:55,982 - src.core.assistant - INFO - Processing voice input: if you have Appiah
2025-05-28 21:54:55,990 - src.ai.deepseek_client.2003906277072 - INFO - API request attempt 1/6
2025-05-28 21:55:06,482 - src.ai.deepseek_client.2003906277072 - INFO - Success metrics updated. Latency: 10.49s, Attempts: 1
2025-05-28 21:55:06,483 - src.ai.deepseek_client.2003906277072 - WARNING - Request attempt 1 failed: 'DeepSeekClient' object has no attribute 'cache_ttl'
2025-05-28 21:55:06,484 - src.ai.deepseek_client.2003906277072 - ERROR - [Perf: N/A] - Error on attempt 1: 'DeepSeekClient' object has no attribute 'cache_ttl'
2025-05-28 21:55:06,485 - src.ai.deepseek_client.2003906277072 - ERROR - Error in analyze_intent: 'total_time'
2025-05-28 21:55:09,742 - src.ui.interface - INFO - Closing application...
2025-05-28 21:55:09,744 - src.core.assistant - INFO - Assistant stopping...
2025-05-28 21:55:09,744 - src.core.context_processor - INFO - Saving session context: session_20250528_215449
2025-05-28 21:55:09,747 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-05-28 21:55:09,806 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001D2927F8210>, 14414.281)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001D292ACA710>
2025-06-04 19:09:11,913 - root - INFO - Logging configured successfully
2025-06-04 19:09:11,914 - __main__ - INFO - Starting AI Assistant...
2025-06-04 19:09:11,915 - src.core.config_manager - INFO - Configuration loaded successfully
2025-06-04 19:09:11,915 - src.data.database_manager - INFO - Database manager initialized
2025-06-04 19:09:11,915 - src.core.context_processor - INFO - Context processor initialized
2025-06-04 19:09:11,916 - src.core.intent_manager - INFO - Intent manager initialized
2025-06-04 19:09:11,916 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-06-04 19:09:11,917 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-06-04 19:09:11,917 - src.actions.system_controller - INFO - System controller initialized
2025-06-04 19:09:11,918 - src.actions.file_controller - INFO - File controller initialized
2025-06-04 19:09:11,918 - src.actions.web_controller - INFO - Web controller initialized
2025-06-04 19:09:11,919 - src.actions.action_executor - INFO - Action executor initialized
2025-06-04 19:09:11,919 - src.core.learning_module - INFO - Learning module initialized
2025-06-04 19:09:11,919 - src.core.assistant - INFO - AI Assistant initialized
2025-06-04 19:09:13,166 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-04 19:09:13,167 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-06-04 19:09:14,616 - src.ui.voice_interface - INFO - Microphone calibrated
2025-06-04 19:09:14,617 - src.ui.voice_interface - INFO - Voice interface initialized
2025-06-04 19:09:14,617 - src.ui.interface - INFO - User interface initialized
2025-06-04 19:09:14,617 - __main__ - INFO - AI Assistant initialized successfully
2025-06-04 19:09:14,618 - src.ui.interface - INFO - Starting user interface...
2025-06-04 19:09:15,120 - src.core.assistant - INFO - Assistant started with session ID: session_20250604_190915
2025-06-04 19:09:15,128 - src.data.database_manager - INFO - Database initialized
2025-06-04 19:09:15,128 - src.core.learning_module - INFO - Learning module started
2025-06-04 19:09:15,591 - src.ui.interface - INFO - GUI created successfully
2025-06-04 19:09:20,951 - src.core.assistant - INFO - Processing text input: hi
2025-06-04 19:09:26,475 - src.core.assistant - INFO - Processing text input: api access?
2025-06-04 19:09:26,483 - src.ai.deepseek_client.2621249592080 - INFO - API request attempt 1/6
2025-06-04 19:09:26,484 - src.ai.deepseek_client.2621249592080 - WARNING - Request attempt 1 failed: Timeout context manager should be used inside a task
2025-06-04 19:09:26,485 - src.ai.deepseek_client.2621249592080 - ERROR - [Perf: N/A] - Error on attempt 1: Timeout context manager should be used inside a task
2025-06-04 19:09:26,486 - src.ai.deepseek_client.2621249592080 - ERROR - Error in analyze_intent: 'total_time'
2025-06-04 19:09:29,717 - src.ui.interface - INFO - Closing application...
2025-06-04 19:09:29,718 - src.core.assistant - INFO - Assistant stopping...
2025-06-04 19:09:29,719 - src.core.context_processor - INFO - Saving session context: session_20250604_190915
2025-06-04 19:09:29,722 - src.ui.interface - ERROR - Error stopping assistant: table sessions has no column named metadata
2025-06-04 19:09:29,772 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000026250B52E50>
2025-07-26 02:30:41,897 - root - INFO - Logging configured successfully
2025-07-26 02:30:41,897 - __main__ - INFO - Starting AI Assistant...
2025-07-26 02:30:41,898 - src.core.config_manager - INFO - Configuration loaded successfully
2025-07-26 02:30:41,898 - src.data.database_manager - INFO - Database manager initialized
2025-07-26 02:30:41,898 - src.core.performance_monitor - INFO - Performance monitor initialized
2025-07-26 02:30:41,900 - src.core.context_processor - INFO - Context processor initialized
2025-07-26 02:30:41,900 - src.core.fallback_manager - INFO - Fallback manager initialized
2025-07-26 02:30:41,900 - src.core.intent_manager - INFO - Intent manager initialized with fallback support
2025-07-26 02:30:41,900 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-07-26 02:30:41,900 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-07-26 02:30:41,900 - src.actions.system_controller - INFO - System controller initialized
2025-07-26 02:30:41,900 - src.actions.file_controller - INFO - File controller initialized
2025-07-26 02:30:41,901 - src.actions.web_controller - INFO - Web controller initialized
2025-07-26 02:30:41,901 - src.actions.action_executor - INFO - Action executor initialized
2025-07-26 02:30:41,901 - src.core.learning_module - INFO - Learning module initialized
2025-07-26 02:30:41,902 - src.core.assistant - INFO - AI Assistant initialized
2025-07-26 02:30:43,145 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-26 02:30:43,146 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-07-26 02:30:44,591 - src.ui.voice_interface - INFO - Microphone calibrated
2025-07-26 02:30:44,591 - src.ui.voice_interface - INFO - Voice interface initialized
2025-07-26 02:30:44,591 - src.ui.interface - INFO - User interface initialized
2025-07-26 02:30:44,592 - __main__ - INFO - AI Assistant initialized successfully
2025-07-26 02:30:44,592 - src.ui.interface - INFO - Starting user interface...
2025-07-26 02:30:45,106 - src.core.assistant - INFO - Assistant started with session ID: session_20250726_023045
2025-07-26 02:30:45,111 - src.data.database_manager - INFO - Database initialized
2025-07-26 02:30:45,111 - src.core.learning_module - INFO - Learning module started
2025-07-26 02:30:45,535 - src.ui.interface - INFO - GUI created successfully
2025-07-26 02:30:51,615 - src.ui.voice_interface - INFO - Recognized speech: yo what's good
2025-07-26 02:30:51,617 - src.core.assistant - INFO - Processing voice input: yo what's good
2025-07-26 02:30:51,624 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 1/6
2025-07-26 02:30:53,010 - src.core.performance_monitor - WARNING - Performance Alert [HIGH]: Error rate (100.00%) exceeds threshold (10.00%)
2025-07-26 02:30:53,010 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:30:54,236 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 2/6
2025-07-26 02:30:54,553 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:30:58,486 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 3/6
2025-07-26 02:30:58,773 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:31:04,815 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 4/6
2025-07-26 02:31:05,090 - src.core.performance_monitor - WARNING - Performance Alert [MEDIUM]: Average response time (6.23s) exceeds threshold (5.0s)
2025-07-26 02:31:05,091 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:31:19,578 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 5/6
2025-07-26 02:31:19,905 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:31:44,568 - src.ai.deepseek_client.1667812917584 - INFO - API request attempt 6/6
2025-07-26 02:31:44,850 - src.ai.deepseek_client.1667812917584 - WARNING - Request failed with status 400. Pattern: 400_API error 400: {"error":{"message":"Model Not Exis. Current success rate: 0.00
2025-07-26 02:32:14,868 - src.ai.deepseek_client.1667812917584 - ERROR - Error in analyze_intent: 'total_time'
2025-07-26 02:32:14,868 - src.core.intent_manager - WARNING - API failure #1: 'total_time'
2025-07-26 02:32:19,460 - src.ui.interface - INFO - Closing application...
2025-07-26 02:32:19,462 - src.core.assistant - INFO - Assistant stopping...
2025-07-26 02:32:19,462 - src.core.context_processor - INFO - Saving session context: session_20250726_023045
2025-07-26 02:32:19,467 - src.core.learning_module - INFO - Learning module stopped
2025-07-26 02:32:19,467 - src.data.database_manager - INFO - Database connections closed
2025-07-26 02:32:19,467 - src.core.assistant - INFO - Assistant stopped
2025-07-26 02:32:19,535 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000184534DC910>, 84588.656)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000184537CAF50>
2025-07-26 02:33:14,051 - root - INFO - Logging configured successfully
2025-07-26 02:33:14,051 - __main__ - INFO - Starting AI Assistant...
2025-07-26 02:33:14,052 - src.core.config_manager - INFO - Configuration loaded successfully
2025-07-26 02:33:14,052 - src.data.database_manager - INFO - Database manager initialized
2025-07-26 02:33:14,053 - src.core.performance_monitor - INFO - Performance monitor initialized
2025-07-26 02:33:14,053 - src.core.context_processor - INFO - Context processor initialized
2025-07-26 02:33:14,053 - src.core.fallback_manager - INFO - Fallback manager initialized
2025-07-26 02:33:14,053 - src.core.intent_manager - INFO - Intent manager initialized with fallback support
2025-07-26 02:33:14,054 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-07-26 02:33:14,054 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-07-26 02:33:14,054 - src.actions.system_controller - INFO - System controller initialized
2025-07-26 02:33:14,054 - src.actions.file_controller - INFO - File controller initialized
2025-07-26 02:33:14,055 - src.actions.web_controller - INFO - Web controller initialized
2025-07-26 02:33:14,055 - src.actions.action_executor - INFO - Action executor initialized
2025-07-26 02:33:14,055 - src.core.learning_module - INFO - Learning module initialized
2025-07-26 02:33:14,056 - src.core.assistant - INFO - AI Assistant initialized
2025-07-26 02:33:14,291 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-26 02:33:14,292 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-07-26 02:33:15,502 - src.ui.voice_interface - INFO - Microphone calibrated
2025-07-26 02:33:15,503 - src.ui.voice_interface - INFO - Voice interface initialized
2025-07-26 02:33:15,503 - src.ui.interface - INFO - User interface initialized
2025-07-26 02:33:15,504 - __main__ - INFO - AI Assistant initialized successfully
2025-07-26 02:33:15,504 - src.ui.interface - INFO - Starting user interface...
2025-07-26 02:33:15,599 - src.core.assistant - INFO - Assistant started with session ID: session_20250726_023315
2025-07-26 02:33:15,604 - src.data.database_manager - INFO - Database initialized
2025-07-26 02:33:15,605 - src.core.learning_module - INFO - Learning module started
2025-07-26 02:33:15,882 - src.ui.interface - INFO - GUI created successfully
2025-07-26 02:33:23,970 - src.ui.voice_interface - INFO - Recognized speech: yo
2025-07-26 02:33:23,972 - src.core.assistant - INFO - Processing voice input: yo
2025-07-26 02:33:23,979 - src.ai.deepseek_client.1375808192848 - INFO - API request attempt 1/6
2025-07-26 02:33:24,658 - src.core.performance_monitor - WARNING - Performance Alert [HIGH]: Error rate (100.00%) exceeds threshold (10.00%)
2025-07-26 02:33:24,658 - src.ai.deepseek_client.1375808192848 - WARNING - Request failed with status 402. Pattern: 402_API error 402: {"error":{"message":"Insufficient B. Current success rate: 0.00
2025-07-26 02:33:26,538 - src.ai.deepseek_client.1375808192848 - INFO - API request attempt 2/6
2025-07-26 02:33:27,123 - src.ai.deepseek_client.1375808192848 - WARNING - Request failed with status 402. Pattern: 402_API error 402: {"error":{"message":"Insufficient B. Current success rate: 0.00
2025-07-26 02:33:30,378 - src.ai.deepseek_client.1375808192848 - INFO - API request attempt 3/6
2025-07-26 02:33:30,951 - src.ai.deepseek_client.1375808192848 - WARNING - Request failed with status 402. Pattern: 402_API error 402: {"error":{"message":"Insufficient B. Current success rate: 0.00
2025-07-26 02:33:36,489 - src.ai.deepseek_client.1375808192848 - INFO - API request attempt 4/6
2025-07-26 02:33:37,075 - src.core.performance_monitor - WARNING - Performance Alert [MEDIUM]: Average response time (5.97s) exceeds threshold (5.0s)
2025-07-26 02:33:37,075 - src.ai.deepseek_client.1375808192848 - WARNING - Request failed with status 402. Pattern: 402_API error 402: {"error":{"message":"Insufficient B. Current success rate: 0.00
2025-07-26 02:33:43,813 - src.ui.interface - INFO - Closing application...
2025-07-26 02:33:43,814 - src.core.assistant - INFO - Assistant stopping...
2025-07-26 02:33:43,814 - src.core.context_processor - INFO - Saving session context: session_20250726_023315
2025-07-26 02:33:43,818 - src.core.learning_module - INFO - Learning module stopped
2025-07-26 02:33:43,819 - src.data.database_manager - INFO - Database connections closed
2025-07-26 02:33:43,820 - src.core.assistant - INFO - Assistant stopped
2025-07-26 02:37:19,215 - root - INFO - Logging configured successfully
2025-07-26 02:37:19,216 - __main__ - INFO - Starting AI Assistant...
2025-07-26 02:37:19,216 - src.core.config_manager - INFO - Configuration loaded successfully
2025-07-26 02:37:19,216 - src.data.database_manager - INFO - Database manager initialized
2025-07-26 02:37:19,217 - src.core.performance_monitor - INFO - Performance monitor initialized
2025-07-26 02:37:19,217 - src.core.context_processor - INFO - Context processor initialized
2025-07-26 02:37:19,217 - src.core.fallback_manager - INFO - Fallback manager initialized
2025-07-26 02:37:19,217 - src.core.intent_manager - INFO - Intent manager initialized with fallback support
2025-07-26 02:37:19,218 - src.actions.keyboard_controller - INFO - Keyboard controller initialized
2025-07-26 02:37:19,218 - src.actions.mouse_controller - INFO - Mouse controller initialized
2025-07-26 02:37:19,218 - src.actions.system_controller - INFO - System controller initialized
2025-07-26 02:37:19,218 - src.actions.file_controller - INFO - File controller initialized
2025-07-26 02:37:19,218 - src.actions.web_controller - INFO - Web controller initialized
2025-07-26 02:37:19,218 - src.actions.action_executor - INFO - Action executor initialized
2025-07-26 02:37:19,220 - src.core.learning_module - INFO - Learning module initialized
2025-07-26 02:37:19,220 - src.core.assistant - INFO - AI Assistant initialized
2025-07-26 02:37:19,454 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-26 02:37:19,454 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comtypes\gen'
2025-07-26 02:37:20,682 - src.ui.voice_interface - INFO - Microphone calibrated
2025-07-26 02:37:20,682 - src.ui.voice_interface - INFO - Voice interface initialized
2025-07-26 02:37:20,682 - src.ui.interface - INFO - User interface initialized
2025-07-26 02:37:20,683 - __main__ - INFO - AI Assistant initialized successfully
2025-07-26 02:37:20,683 - src.ui.interface - INFO - Starting user interface...
2025-07-26 02:37:20,750 - src.core.assistant - INFO - Assistant started with session ID: session_20250726_023720
2025-07-26 02:37:20,754 - src.data.database_manager - INFO - Database initialized
2025-07-26 02:37:20,754 - src.core.learning_module - INFO - Learning module started
2025-07-26 02:37:21,036 - src.ui.interface - INFO - GUI created successfully
2025-07-26 02:37:28,907 - src.ui.voice_interface - INFO - Recognized speech: how you doing
2025-07-26 02:37:28,910 - src.core.assistant - INFO - Processing voice input: how you doing
2025-07-26 02:37:28,917 - src.ai.deepseek_client.2818568615312 - INFO - API request attempt 1/6
2025-07-26 02:37:29,776 - src.core.performance_monitor - WARNING - Performance Alert [HIGH]: Error rate (100.00%) exceeds threshold (10.00%)
2025-07-26 02:37:29,777 - src.ai.deepseek_client.2818568615312 - WARNING - Request failed with status 429. Pattern: 429_API error 429: {"error":{"message":"Provider retur. Current success rate: 0.00
2025-07-26 02:37:33,318 - src.ai.deepseek_client.2818568615312 - INFO - API request attempt 2/6
2025-07-26 02:37:34,395 - src.ai.deepseek_client.2818568615312 - INFO - Success metrics updated. Latency: 5.48s, Attempts: 2
2025-07-26 02:37:34,400 - src.ai.deepseek_client.2818568615312 - WARNING - Request attempt 2 failed: 'DeepSeekClient' object has no attribute 'cache_ttl'
2025-07-26 02:37:34,400 - src.ai.deepseek_client.2818568615312 - ERROR - [Perf: N/A] - Error on attempt 2: 'DeepSeekClient' object has no attribute 'cache_ttl'
2025-07-26 02:37:34,401 - src.ai.deepseek_client.2818568615312 - ERROR - Error in analyze_intent: 'total_time'
2025-07-26 02:37:34,401 - src.core.intent_manager - WARNING - API failure #1: 'total_time'
2025-07-26 02:37:54,259 - src.core.assistant - INFO - Processing text input: hi
2025-07-26 02:38:07,571 - src.core.assistant - INFO - Processing text input: find top geopolitacl news from today
2025-07-26 02:38:07,577 - src.ai.deepseek_client.2818568615312 - INFO - API request attempt 1/3
2025-07-26 02:38:07,577 - src.ai.deepseek_client.2818568615312 - WARNING - Request attempt 1 failed: Timeout context manager should be used inside a task
2025-07-26 02:38:07,577 - src.ai.deepseek_client.2818568615312 - ERROR - [Perf: N/A] - Error on attempt 1: Timeout context manager should be used inside a task
2025-07-26 02:38:07,578 - src.ai.deepseek_client.2818568615312 - ERROR - Error in analyze_intent: 'total_time'
2025-07-26 02:38:07,578 - src.core.intent_manager - WARNING - API failure #1: 'total_time'
2025-07-26 02:38:20,105 - src.ui.interface - INFO - Closing application...
2025-07-26 02:38:20,106 - src.core.assistant - INFO - Assistant stopping...
2025-07-26 02:38:20,107 - src.core.context_processor - INFO - Saving session context: session_20250726_023720
2025-07-26 02:38:20,111 - src.core.learning_module - INFO - Learning module stopped
2025-07-26 02:38:20,112 - src.data.database_manager - INFO - Database connections closed
2025-07-26 02:38:20,112 - src.core.assistant - INFO - Assistant stopped
2025-07-26 02:38:20,172 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000002904127C910>, 84938.203)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000029041567C10>
