"""
Action executor for keyboard, mouse, and system operations.
"""

import asyncio
import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from .keyboard_controller import KeyboardController
from .mouse_controller import Mouse<PERSON>ontroller
from .system_controller import SystemController
from .file_controller import FileController
from .web_controller import WebController


class ActionExecutor:
    """Executes various types of actions on the system."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the action executor."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize controllers
        self.keyboard = KeyboardController(config)
        self.mouse = MouseController(config)
        self.system = SystemController(config)
        self.file = FileController(config)
        self.web = WebController(config)
        
        # Pending actions (for confirmation)
        self.pending_actions: Dict[str, Dict[str, Any]] = {}
        
        # Safety settings
        self.safety_checks = config.get('safety_checks', True)
        
        self.logger.info("Action executor initialized")
    
    async def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a single action.
        
        Args:
            action: Action dictionary with type, command, and parameters
            
        Returns:
            Result dictionary with success status and details
        """
        try:
            action_type = action.get('type', '').lower()
            command = action.get('command', '')
            parameters = action.get('parameters', {})
            
            self.logger.info(f"Executing {action_type} action: {command}")
            
            # Safety check
            if self.safety_checks and self._is_dangerous_action(action):
                return await self._queue_for_confirmation(action)
            
            # Route to appropriate controller
            if action_type == 'keyboard':
                result = await self.keyboard.execute(command, parameters)
            elif action_type == 'mouse':
                result = await self.mouse.execute(command, parameters)
            elif action_type == 'system':
                result = await self.system.execute(command, parameters)
            elif action_type == 'file':
                result = await self.file.execute(command, parameters)
            elif action_type == 'web':
                result = await self.web.execute(command, parameters)
            else:
                result = {
                    'success': False,
                    'error': f'Unknown action type: {action_type}'
                }
            
            # Add metadata
            result['action_id'] = str(uuid.uuid4())
            result['timestamp'] = datetime.now().isoformat()
            result['action'] = action
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing action: {e}")
            return {
                'success': False,
                'error': str(e),
                'action_id': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'action': action
            }
    
    async def execute_pending_action(self, action_id: str) -> Dict[str, Any]:
        """Execute a pending action that was queued for confirmation."""
        if action_id not in self.pending_actions:
            return {
                'success': False,
                'error': 'Action not found in pending queue'
            }
        
        action = self.pending_actions.pop(action_id)
        
        # Temporarily disable safety checks for confirmed actions
        original_safety = self.safety_checks
        self.safety_checks = False
        
        try:
            result = await self.execute_action(action['action'])
            return result
        finally:
            self.safety_checks = original_safety
    
    async def cancel_pending_action(self, action_id: str) -> bool:
        """Cancel a pending action."""
        if action_id in self.pending_actions:
            del self.pending_actions[action_id]
            return True
        return False
    
    async def _queue_for_confirmation(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Queue an action for user confirmation."""
        action_id = str(uuid.uuid4())
        
        self.pending_actions[action_id] = {
            'action': action,
            'timestamp': datetime.now().isoformat()
        }
        
        return {
            'success': False,
            'requires_confirmation': True,
            'action_id': action_id,
            'message': f"This action requires confirmation: {action.get('command', 'Unknown action')}",
            'action': action
        }
    
    def _is_dangerous_action(self, action: Dict[str, Any]) -> bool:
        """
        Determine if an action is potentially dangerous and requires confirmation.
        
        Args:
            action: Action to evaluate
            
        Returns:
            True if action is considered dangerous
        """
        dangerous_patterns = [
            # System commands
            'shutdown', 'restart', 'delete', 'format', 'rm -rf',
            # File operations
            'delete_file', 'remove_directory', 'format_drive',
            # Network operations
            'download_file', 'upload_file', 'send_email',
            # Registry/system changes
            'registry_edit', 'system_config'
        ]
        
        command = action.get('command', '').lower()
        action_type = action.get('type', '').lower()
        
        # Check for dangerous patterns
        for pattern in dangerous_patterns:
            if pattern in command:
                return True
        
        # System actions are generally more dangerous
        if action_type == 'system':
            return True
        
        # File operations on system directories
        if action_type == 'file':
            path = action.get('parameters', {}).get('path', '')
            dangerous_paths = ['C:\\Windows', 'C:\\System32', '/etc', '/usr', '/var']
            for dangerous_path in dangerous_paths:
                if dangerous_path.lower() in path.lower():
                    return True
        
        return False
    
    def get_pending_actions(self) -> List[Dict[str, Any]]:
        """Get list of pending actions."""
        return [
            {
                'action_id': action_id,
                'action': data['action'],
                'timestamp': data['timestamp']
            }
            for action_id, data in self.pending_actions.items()
        ]
    
    def clear_pending_actions(self):
        """Clear all pending actions."""
        self.pending_actions.clear()
