{"deepseek": {"api_key": "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", "base_url": "https://openrouter.ai/api/v1", "model": "moonshotai/kimi-k2:free", "timeout": 30.0, "max_retries": 3, "min_request_interval": 0.5, "temperature": 0.3, "max_tokens": 300, "enable_caching": true, "enable_self_improvement": true, "performance_monitoring": true}, "fallback": {"enabled": true, "max_failures_before_fallback": 3, "enable_pattern_matching": true, "enable_local_calculations": true, "custom_responses": {"greeting": ["Hello! I'm here to help, even when my main systems are offline.", "Hi there! I'm operating in local mode but ready to assist."]}}, "performance": {"enabled": true, "response_time_threshold": 5.0, "error_rate_threshold": 0.1, "success_rate_threshold": 0.9, "alert_cooldown_minutes": 15, "metrics_retention_days": 30}, "assistant": {"name": "Assistant", "wake_word": "hey assistant", "voice_enabled": true, "auto_execute": false, "confirmation_required": true}, "voice": {"tts_engine": "pyttsx3", "tts_rate": 200, "tts_volume": 0.8, "stt_timeout": 5, "stt_phrase_timeout": 1}, "automation": {"mouse_speed": 0.5, "keyboard_delay": 0.1, "screenshot_quality": 80, "safety_checks": true}, "learning": {"enabled": true, "feedback_collection": true, "auto_improvement": true, "context_retention_days": 30}, "database": {"path": "data/assistant.db", "backup_enabled": true, "backup_interval_hours": 24}, "logging": {"level": "INFO", "file": "logs/assistant.log", "max_size_mb": 10, "backup_count": 5}}