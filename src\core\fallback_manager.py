"""
Fallback manager for handling API failures and providing alternative responses.
"""

import logging
import json
import random
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class FallbackResponse:
    """Fallback response data structure."""
    response: str
    confidence: float
    source: str
    timestamp: datetime


class FallbackManager:
    """Manages fallback responses when primary API is unavailable."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize fallback manager."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Fallback response templates
        self.fallback_responses = {
            'greeting': [
                "Hello! I'm currently experiencing some connectivity issues, but I'm here to help as best I can.",
                "Hi there! While I'm having some technical difficulties, I'll do my best to assist you.",
                "Greetings! I'm operating in offline mode right now, but I can still help with basic tasks."
            ],
            'help': [
                "I can help you with basic tasks like file operations, system commands, and general assistance. What would you like to do?",
                "Even in offline mode, I can assist with local tasks, file management, and basic automation. How can I help?",
                "I'm available for local operations and basic assistance. What do you need help with?"
            ],
            'error': [
                "I'm sorry, I'm experiencing technical difficulties right now. Please try again in a moment.",
                "I'm having trouble processing that request at the moment. Could you try rephrasing it?",
                "I'm currently in limited mode due to connectivity issues. I can help with basic tasks though."
            ],
            'unknown': [
                "I'm not sure I understand that request. Could you please rephrase it?",
                "I'm having trouble understanding that. Can you try asking in a different way?",
                "I'm not quite sure what you mean. Could you provide more details?"
            ],
            'task_completion': [
                "I've completed that task to the best of my ability in offline mode.",
                "Task completed using local resources.",
                "Done! I've handled that using available local capabilities."
            ]
        }
        
        # Pattern-based responses
        self.pattern_responses = {
            'time': self._get_current_time,
            'date': self._get_current_date,
            'weather': self._weather_fallback,
            'calculation': self._basic_calculation,
            'file': self._file_operation_help,
            'system': self._system_info_help
        }
        
        # Response history for learning
        self.response_history = []
        self.max_history = 100
        
        self.logger.info("Fallback manager initialized")

    def get_fallback_response(
        self, 
        user_input: str, 
        context: Dict[str, Any], 
        error_type: Optional[str] = None
    ) -> FallbackResponse:
        """Get appropriate fallback response."""
        user_input_lower = user_input.lower().strip()
        
        # Try pattern-based responses first
        for pattern, handler in self.pattern_responses.items():
            if pattern in user_input_lower:
                try:
                    response = handler(user_input, context)
                    if response:
                        return FallbackResponse(
                            response=response,
                            confidence=0.7,
                            source=f"pattern_{pattern}",
                            timestamp=datetime.now()
                        )
                except Exception as e:
                    self.logger.warning(f"Pattern handler {pattern} failed: {e}")
        
        # Determine response category
        category = self._categorize_input(user_input_lower, error_type)
        
        # Get response from category
        responses = self.fallback_responses.get(category, self.fallback_responses['unknown'])
        response = random.choice(responses)
        
        # Personalize response if possible
        response = self._personalize_response(response, context)
        
        fallback = FallbackResponse(
            response=response,
            confidence=0.5,
            source=f"fallback_{category}",
            timestamp=datetime.now()
        )
        
        # Store in history
        self._store_response_history(user_input, fallback)
        
        return fallback

    def _categorize_input(self, user_input: str, error_type: Optional[str] = None) -> str:
        """Categorize user input to determine appropriate fallback."""
        if error_type:
            return 'error'
        
        # Greeting patterns
        greeting_words = ['hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon']
        if any(word in user_input for word in greeting_words):
            return 'greeting'
        
        # Help patterns
        help_words = ['help', 'assist', 'what can you do', 'commands', 'support']
        if any(word in user_input for word in help_words):
            return 'help'
        
        # Task completion indicators
        task_words = ['do', 'create', 'make', 'run', 'execute', 'perform']
        if any(word in user_input for word in task_words):
            return 'task_completion'
        
        return 'unknown'

    def _personalize_response(self, response: str, context: Dict[str, Any]) -> str:
        """Personalize response based on context."""
        # Add user's name if available
        if 'user_name' in context:
            response = f"{context['user_name']}, {response.lower()}"
        
        # Add time-based greetings
        current_hour = datetime.now().hour
        if 'hello' in response.lower() or 'hi' in response.lower():
            if 5 <= current_hour < 12:
                response = response.replace('Hello', 'Good morning')
            elif 12 <= current_hour < 17:
                response = response.replace('Hello', 'Good afternoon')
            elif 17 <= current_hour < 22:
                response = response.replace('Hello', 'Good evening')
        
        return response

    def _get_current_time(self, user_input: str, context: Dict[str, Any]) -> str:
        """Get current time."""
        now = datetime.now()
        return f"The current time is {now.strftime('%I:%M %p')}."

    def _get_current_date(self, user_input: str, context: Dict[str, Any]) -> str:
        """Get current date."""
        now = datetime.now()
        return f"Today is {now.strftime('%A, %B %d, %Y')}."

    def _weather_fallback(self, user_input: str, context: Dict[str, Any]) -> str:
        """Weather fallback response."""
        return "I'm unable to check the weather right now due to connectivity issues. You might want to check a weather app or website."

    def _basic_calculation(self, user_input: str, context: Dict[str, Any]) -> str:
        """Handle basic calculations."""
        try:
            # Simple math expression evaluation (very basic)
            import re
            
            # Extract numbers and basic operators
            math_pattern = r'(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)'
            match = re.search(math_pattern, user_input)
            
            if match:
                num1, operator, num2 = match.groups()
                num1, num2 = float(num1), float(num2)
                
                if operator == '+':
                    result = num1 + num2
                elif operator == '-':
                    result = num1 - num2
                elif operator == '*':
                    result = num1 * num2
                elif operator == '/':
                    if num2 != 0:
                        result = num1 / num2
                    else:
                        return "I can't divide by zero!"
                else:
                    return None
                
                return f"The result is {result}."
            
        except Exception:
            pass
        
        return "I can help with basic calculations like addition, subtraction, multiplication, and division. Try asking something like '5 + 3' or '10 * 2'."

    def _file_operation_help(self, user_input: str, context: Dict[str, Any]) -> str:
        """File operation help."""
        return "I can help with basic file operations like creating, reading, and organizing files. What specific file task do you need help with?"

    def _system_info_help(self, user_input: str, context: Dict[str, Any]) -> str:
        """System information help."""
        import platform
        import psutil
        
        try:
            system_info = f"System: {platform.system()} {platform.release()}"
            cpu_count = psutil.cpu_count()
            memory = psutil.virtual_memory()
            
            return f"{system_info}, CPU cores: {cpu_count}, Memory: {memory.total // (1024**3)}GB total, {memory.percent}% used."
        except Exception:
            return "I can provide basic system information. Your system details are available through local commands."

    def _store_response_history(self, user_input: str, fallback: FallbackResponse):
        """Store response in history for learning."""
        self.response_history.append({
            'user_input': user_input,
            'response': fallback.response,
            'source': fallback.source,
            'timestamp': fallback.timestamp.isoformat(),
            'confidence': fallback.confidence
        })
        
        # Limit history size
        if len(self.response_history) > self.max_history:
            self.response_history = self.response_history[-self.max_history:]

    def get_response_analytics(self) -> Dict[str, Any]:
        """Get analytics about fallback responses."""
        if not self.response_history:
            return {'total_responses': 0}
        
        # Count by source
        source_counts = {}
        confidence_sum = 0
        
        for entry in self.response_history:
            source = entry['source']
            source_counts[source] = source_counts.get(source, 0) + 1
            confidence_sum += entry['confidence']
        
        avg_confidence = confidence_sum / len(self.response_history)
        
        return {
            'total_responses': len(self.response_history),
            'source_distribution': source_counts,
            'average_confidence': avg_confidence,
            'most_common_source': max(source_counts.items(), key=lambda x: x[1])[0] if source_counts else None
        }

    def improve_responses(self, feedback: Dict[str, Any]):
        """Improve responses based on user feedback."""
        # This could be enhanced with machine learning
        # For now, just log the feedback
        self.logger.info(f"Received feedback for response improvement: {feedback}")
        
        # Simple improvement: adjust confidence based on feedback
        if 'response_id' in feedback and 'rating' in feedback:
            # Find and update the response in history
            for entry in self.response_history:
                if entry.get('id') == feedback['response_id']:
                    # Adjust confidence based on rating
                    rating = feedback['rating']  # Assume 1-5 scale
                    if rating >= 4:
                        entry['confidence'] = min(1.0, entry['confidence'] + 0.1)
                    elif rating <= 2:
                        entry['confidence'] = max(0.1, entry['confidence'] - 0.1)
                    break

    def export_fallback_data(self) -> Dict[str, Any]:
        """Export fallback data for analysis."""
        return {
            'response_history': self.response_history,
            'analytics': self.get_response_analytics(),
            'fallback_templates': self.fallback_responses,
            'export_timestamp': datetime.now().isoformat()
        }

    def load_custom_responses(self, custom_responses: Dict[str, List[str]]):
        """Load custom response templates."""
        for category, responses in custom_responses.items():
            if category in self.fallback_responses:
                self.fallback_responses[category].extend(responses)
            else:
                self.fallback_responses[category] = responses
        
        self.logger.info(f"Loaded custom responses for categories: {list(custom_responses.keys())}")
