"""
Comprehensive test suite for DeepSeek API integration.
"""

import pytest
import asyncio
import json
import aiohttp
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from src.ai.deepseek_client import DeepSeekClient
from src.core.intent_manager import IntentManager
from src.core.context_processor import ContextProcessor
from src.data.database_manager import DatabaseManager


class TestDeepSeekClient:
    """Test cases for DeepSeek client functionality."""

    @pytest.fixture
    def config(self):
        """Test configuration."""
        return {
            "api_key": "test-api-key",
            "base_url": "https://api.deepseek.com/v1",
            "model": "deepseek-chat",
            "timeout": 30.0,
            "max_retries": 3,
            "min_request_interval": 0.5,
            "temperature": 0.3,
            "max_tokens": 300,
            "enable_caching": True,
            "enable_self_improvement": True,
            "performance_monitoring": True
        }

    @pytest.fixture
    def deepseek_client(self, config):
        """Create DeepSeek client instance."""
        return DeepSeekClient(config)

    @pytest.mark.asyncio
    async def test_client_initialization(self, deepseek_client):
        """Test client initialization."""
        assert deepseek_client.api_key == "test-api-key"
        assert deepseek_client.base_url == "https://api.deepseek.com/v1"
        assert deepseek_client.model == "deepseek-chat"
        assert deepseek_client.timeout == 30.0
        assert deepseek_client.max_retries == 3

    @pytest.mark.asyncio
    async def test_session_management(self, deepseek_client):
        """Test session start and stop."""
        await deepseek_client.start()
        assert deepseek_client.session is not None
        assert not deepseek_client.session.closed

        await deepseek_client.stop()
        assert deepseek_client.session.closed

    @pytest.mark.asyncio
    async def test_simple_commands(self, deepseek_client):
        """Test simple command handling."""
        # Test greeting
        result = await deepseek_client.analyze_intent("hello", {})
        assert result["success"] is True
        assert "Hello" in result["response"]
        assert result["intent"] == "greeting"

        # Test status check
        result = await deepseek_client.analyze_intent("how are you", {})
        assert result["success"] is True
        assert "functioning well" in result["response"]
        assert result["intent"] == "status_check"

        # Test help request
        result = await deepseek_client.analyze_intent("help", {})
        assert result["success"] is True
        assert "help you" in result["response"]
        assert result["intent"] == "help_request"

    @pytest.mark.asyncio
    async def test_api_request_success(self, deepseek_client):
        """Test successful API request."""
        mock_response = {
            "choices": [
                {
                    "message": {
                        "content": "This is a test response"
                    }
                }
            ],
            "usage": {"total_tokens": 50},
            "model": "deepseek-chat"
        }

        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response_obj = AsyncMock()
            mock_response_obj.status = 200
            mock_response_obj.json.return_value = mock_response
            mock_post.return_value.__aenter__.return_value = mock_response_obj

            await deepseek_client.start()
            messages = [{"role": "user", "content": "test message"}]
            result = await deepseek_client.generate_response(messages)

            assert result["success"] is True
            assert "This is a test response" in result["content"]
            assert result["usage"]["total_tokens"] == 50

    @pytest.mark.asyncio
    async def test_api_request_failure(self, deepseek_client):
        """Test API request failure handling."""
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response_obj = AsyncMock()
            mock_response_obj.status = 500
            mock_response_obj.text.return_value = "Internal Server Error"
            mock_post.return_value.__aenter__.return_value = mock_response_obj

            await deepseek_client.start()
            messages = [{"role": "user", "content": "test message"}]
            result = await deepseek_client.generate_response(messages)

            assert result["success"] is False
            assert "error" in result

    @pytest.mark.asyncio
    async def test_rate_limiting(self, deepseek_client):
        """Test rate limiting functionality."""
        await deepseek_client.start()
        
        # Make multiple rapid requests
        start_time = asyncio.get_event_loop().time()
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response_obj = AsyncMock()
            mock_response_obj.status = 200
            mock_response_obj.json.return_value = {
                "choices": [{"message": {"content": "test"}}],
                "usage": {"total_tokens": 10}
            }
            mock_post.return_value.__aenter__.return_value = mock_response_obj

            messages = [{"role": "user", "content": "test"}]
            await deepseek_client.generate_response(messages)
            await deepseek_client.generate_response(messages)

        end_time = asyncio.get_event_loop().time()
        
        # Should have some delay due to rate limiting
        assert end_time - start_time >= deepseek_client.min_request_interval

    @pytest.mark.asyncio
    async def test_caching(self, deepseek_client):
        """Test response caching."""
        await deepseek_client.start()
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response_obj = AsyncMock()
            mock_response_obj.status = 200
            mock_response_obj.json.return_value = {
                "choices": [{"message": {"content": "cached response"}}],
                "usage": {"total_tokens": 10}
            }
            mock_post.return_value.__aenter__.return_value = mock_response_obj

            # First request should hit API
            result1 = await deepseek_client.analyze_intent("test query", {})
            
            # Second identical request should use cache
            result2 = await deepseek_client.analyze_intent("test query", {})
            
            # Should only have made one API call
            assert mock_post.call_count <= 1
            assert result1["response"] == result2["response"]


class TestIntentManager:
    """Test cases for Intent Manager integration."""

    @pytest.fixture
    def mock_deepseek_client(self):
        """Mock DeepSeek client."""
        client = AsyncMock()
        client.analyze_intent.return_value = {
            "success": True,
            "response": "Test response",
            "intent": "test_intent",
            "confidence": 0.9
        }
        return client

    @pytest.fixture
    def intent_manager(self, mock_deepseek_client):
        """Create Intent Manager instance."""
        return IntentManager(mock_deepseek_client)

    @pytest.mark.asyncio
    async def test_intent_analysis(self, intent_manager, mock_deepseek_client):
        """Test intent analysis."""
        context = {"session_id": "test-session"}
        result = await intent_manager.analyze_intent("test input", context)

        assert result["intent"] == "test_intent"
        assert result["response"] == "Test response"
        mock_deepseek_client.analyze_intent.assert_called_once_with("test input", context)

    @pytest.mark.asyncio
    async def test_intent_analysis_error(self, intent_manager, mock_deepseek_client):
        """Test intent analysis error handling."""
        mock_deepseek_client.analyze_intent.side_effect = Exception("API Error")
        
        result = await intent_manager.analyze_intent("test input", {})
        
        assert result["intent"] == "error"
        assert result["confidence"] == 0.0
        assert "error" in result["response"]


class TestContextProcessor:
    """Test cases for Context Processor integration."""

    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager."""
        db_manager = Mock()
        db_manager.db_path = "test.db"
        return db_manager

    @pytest.fixture
    def mock_deepseek_client(self):
        """Mock DeepSeek client."""
        return AsyncMock()

    @pytest.fixture
    def context_processor(self, mock_db_manager, mock_deepseek_client):
        """Create Context Processor instance."""
        return ContextProcessor(mock_db_manager, mock_deepseek_client)

    @pytest.mark.asyncio
    async def test_context_update(self, context_processor):
        """Test context update functionality."""
        with patch('aiosqlite.connect') as mock_connect:
            mock_db = AsyncMock()
            mock_connect.return_value.__aenter__.return_value = mock_db

            result = await context_processor.update_context(
                "test message", "test-session", False, {"topic": "test"}
            )

            assert result["session_id"] == "test-session"
            assert len(result["recent_messages"]) == 1
            assert result["recent_messages"][0]["message"] == "test message"

    @pytest.mark.asyncio
    async def test_context_summarization(self, context_processor):
        """Test context summarization when limit exceeded."""
        # Add many messages to trigger summarization
        session_id = "test-session"
        context_processor.session_contexts[session_id] = []
        
        # Add messages beyond the limit
        for i in range(25):  # Exceeds max_context_length of 20
            context_processor.session_contexts[session_id].append({
                "message": f"Message {i}",
                "timestamp": datetime.now().isoformat(),
                "is_assistant": i % 2 == 0,
                "metadata": {"topic": f"topic_{i}"}
            })

        with patch('aiosqlite.connect') as mock_connect:
            mock_db = AsyncMock()
            mock_connect.return_value.__aenter__.return_value = mock_db

            await context_processor._summarize_context(session_id)

            # Should have reduced the context length
            assert len(context_processor.session_contexts[session_id]) < 25
            
            # Should contain a summary message
            summary_found = any(
                "Context Summary" in msg["message"] 
                for msg in context_processor.session_contexts[session_id]
            )
            assert summary_found


class TestDatabaseIntegration:
    """Test cases for database integration."""

    @pytest.fixture
    def db_config(self):
        """Database configuration for testing."""
        return {
            "path": ":memory:",  # Use in-memory database for testing
            "backup_enabled": False
        }

    @pytest.fixture
    async def db_manager(self, db_config):
        """Create database manager instance."""
        manager = DatabaseManager(db_config)
        await manager.initialize()
        return manager

    @pytest.mark.asyncio
    async def test_database_initialization(self, db_manager):
        """Test database initialization."""
        assert db_manager.is_connected()

    @pytest.mark.asyncio
    async def test_table_creation(self, db_manager):
        """Test that all required tables are created."""
        import aiosqlite
        
        async with aiosqlite.connect(db_manager.db_path) as db:
            # Check if tables exist
            cursor = await db.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            tables = [row[0] for row in await cursor.fetchall()]
            
            expected_tables = [
                'context', 'sessions', 'learning_data', 
                'context_summaries', 'api_metrics', 'config_history'
            ]
            
            for table in expected_tables:
                assert table in tables


if __name__ == "__main__":
    pytest.main([__file__])
