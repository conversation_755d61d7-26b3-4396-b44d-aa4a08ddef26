# Siri-like AI Assistant

A locally-running AI assistant powered by DeepSeek AI, capable of performing system tasks through keyboard/mouse automation with context awareness and self-improvement capabilities.

## Features

- 🎤 Voice and text input/output
- 🖱️ Keyboard and mouse automation
- 🧠 Context-aware conversations
- 📈 Self-learning and improvement
- 🔧 Modular architecture
- 🌐 DeepSeek AI integration

## Architecture

The assistant is built with a modular architecture consisting of:

- **User Interface Layer**: Voice/text interface with TTS/STT
- **Core Processing Layer**: Command management, context processing, intent recognition
- **Action Layer**: System automation (keyboard, mouse, file operations)
- **AI Integration**: DeepSeek AI API
- **Data Layer**: Context and learning databases

## Quick Start

1. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

2. Configure your OpenRouter API key:

   ```bash
   cp config/config.example.json config/config.json
   # Edit config.json with your API key
   ```

3. Run the assistant:

   ```bash
   python main.py
   ```

## Project Structure

```
├── src/
│   ├── core/           # Core processing logic
│   ├── ui/             # User interface components
│   ├── actions/        # System action modules
│   ├── ai/             # AI integration
│   └── data/           # Data management
├── config/             # Configuration files
├── tests/              # Test suites
├── docs/               # Documentation
└── requirements.txt    # Dependencies
```

## Development

See [DEVELOPMENT.md](docs/DEVELOPMENT.md) for detailed development guidelines.

## License

MIT License
