#!/usr/bin/env python3
"""
Simple test to verify API key and endpoint.
"""

import asyncio
import aiohttp
import json

async def test_api_key():
    """Test the API key with a simple request."""
    
    # Load config
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    api_key = config['deepseek']['api_key']
    base_url = config['deepseek']['base_url']
    model = config['deepseek']['model']
    
    print(f"Testing API key: {api_key[:20]}...")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": "Hello, just testing the API connection."}
        ],
        "max_tokens": 50,
        "temperature": 0.3
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("\nMaking API request...")
            async with session.post(
                f"{base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                print(f"Status Code: {response.status}")
                print(f"Headers: {dict(response.headers)}")
                
                if response.status == 200:
                    data = await response.json()
                    print("✅ API call successful!")
                    print(f"Response: {data.get('choices', [{}])[0].get('message', {}).get('content', 'No content')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ API call failed!")
                    print(f"Error: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_key())
    if success:
        print("\n🎉 API key test passed!")
    else:
        print("\n⚠️ API key test failed!")
