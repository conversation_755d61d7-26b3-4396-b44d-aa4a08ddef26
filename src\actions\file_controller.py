"""
File operations controller.
"""

import asyncio
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any


class FileController:
    """Handles file and directory operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the file controller."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("File controller initialized")
    
    async def execute(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a file operation command."""
        try:
            command = command.lower()
            
            if command == 'read_file':
                return await self._read_file(parameters)
            elif command == 'write_file':
                return await self._write_file(parameters)
            elif command == 'list_directory':
                return await self._list_directory(parameters)
            elif command == 'create_directory':
                return await self._create_directory(parameters)
            elif command == 'copy_file':
                return await self._copy_file(parameters)
            elif command == 'move_file':
                return await self._move_file(parameters)
            else:
                return {
                    'success': False,
                    'error': f'Unknown file command: {command}'
                }
                
        except Exception as e:
            self.logger.error(f"File command error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _read_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Read contents of a file."""
        file_path = parameters.get('path', '')
        
        if not file_path:
            return {
                'success': False,
                'error': 'No file path specified'
            }
        
        try:
            path = Path(file_path)
            if not path.exists():
                return {
                    'success': False,
                    'error': f'File not found: {file_path}'
                }
            
            content = path.read_text(encoding='utf-8')
            
            return {
                'success': True,
                'content': content,
                'size': len(content)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to read file: {e}'
            }
    
    async def _write_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Write content to a file."""
        file_path = parameters.get('path', '')
        content = parameters.get('content', '')
        append = parameters.get('append', False)
        
        if not file_path:
            return {
                'success': False,
                'error': 'No file path specified'
            }
        
        try:
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            if append:
                path.write_text(content, encoding='utf-8')
            else:
                with path.open('a', encoding='utf-8') as f:
                    f.write(content)
            
            return {
                'success': True,
                'message': f'{"Appended to" if append else "Wrote"} file: {file_path}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to write file: {e}'
            }
    
    async def _list_directory(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """List contents of a directory."""
        dir_path = parameters.get('path', '.')
        
        try:
            path = Path(dir_path)
            if not path.exists():
                return {
                    'success': False,
                    'error': f'Directory not found: {dir_path}'
                }
            
            if not path.is_dir():
                return {
                    'success': False,
                    'error': f'Path is not a directory: {dir_path}'
                }
            
            items = []
            for item in path.iterdir():
                items.append({
                    'name': item.name,
                    'type': 'directory' if item.is_dir() else 'file',
                    'size': item.stat().st_size if item.is_file() else None
                })
            
            return {
                'success': True,
                'items': items,
                'count': len(items)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to list directory: {e}'
            }
    
    async def _create_directory(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Create a directory."""
        dir_path = parameters.get('path', '')
        
        if not dir_path:
            return {
                'success': False,
                'error': 'No directory path specified'
            }
        
        try:
            path = Path(dir_path)
            path.mkdir(parents=True, exist_ok=True)
            
            return {
                'success': True,
                'message': f'Created directory: {dir_path}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to create directory: {e}'
            }
    
    async def _copy_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Copy a file."""
        source = parameters.get('source', '')
        destination = parameters.get('destination', '')
        
        if not source or not destination:
            return {
                'success': False,
                'error': 'Source and destination paths required'
            }
        
        try:
            shutil.copy2(source, destination)
            
            return {
                'success': True,
                'message': f'Copied {source} to {destination}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to copy file: {e}'
            }
    
    async def _move_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Move a file."""
        source = parameters.get('source', '')
        destination = parameters.get('destination', '')
        
        if not source or not destination:
            return {
                'success': False,
                'error': 'Source and destination paths required'
            }
        
        try:
            shutil.move(source, destination)
            
            return {
                'success': True,
                'message': f'Moved {source} to {destination}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to move file: {e}'
            }
