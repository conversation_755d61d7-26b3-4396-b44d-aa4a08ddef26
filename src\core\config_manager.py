"""
Configuration management for the AI Assistant.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any
import logging


class ConfigManager:
    """Manages configuration loading and validation."""

    def __init__(self, config_path: str = "config/config.json"):
        """Initialize the config manager."""
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)

    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            if not self.config_path.exists():
                self.logger.warning(
                    f"Config file not found: {self.config_path}")
                return self._create_default_config()

            with open(self.config_path, 'r') as f:
                config = json.load(f)

            # Validate and fill missing values
            config = self._validate_config(config)

            self.logger.info("Configuration loaded successfully")
            return config

        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            return self._create_default_config()

    def save_config(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file."""
        try:
            # Ensure config directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)

            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)

            self.logger.info("Configuration saved successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            return False

    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and complete configuration."""
        default_config = self._create_default_config()

        # Merge with defaults
        for section, values in default_config.items():
            if section not in config:
                config[section] = values
            else:
                for key, value in values.items():
                    if key not in config[section]:
                        config[section][key] = value

        # Validate API key
        if not config['deepseek']['api_key'] or config['deepseek']['api_key'] == "YOUR_DEEPSEEK_API_KEY_HERE":
            self.logger.warning("DeepSeek API key not configured")

        return config

    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration."""
        return {
            "deepseek": {
                "api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "base_url": "https://api.deepseek.com/v1",
                "model": "deepseek-chat",
                "timeout": 30.0,
                "max_retries": 3,
                "min_request_interval": 0.5,
                "temperature": 0.3,
                "max_tokens": 300,
                "enable_caching": True,
                "enable_self_improvement": True,
                "performance_monitoring": True
            },
            "assistant": {
                "name": "Assistant",
                "wake_word": "hey assistant",
                "voice_enabled": True,
                "auto_execute": False,
                "confirmation_required": True
            },
            "voice": {
                "tts_engine": "pyttsx3",
                "tts_rate": 200,
                "tts_volume": 0.8,
                "stt_timeout": 5,
                "stt_phrase_timeout": 1
            },
            "automation": {
                "mouse_speed": 0.5,
                "keyboard_delay": 0.1,
                "screenshot_quality": 80,
                "safety_checks": True
            },
            "learning": {
                "enabled": True,
                "feedback_collection": True,
                "auto_improvement": True,
                "context_retention_days": 30
            },
            "database": {
                "path": "data/assistant.db",
                "backup_enabled": True,
                "backup_interval_hours": 24
            },
            "logging": {
                "level": "INFO",
                "file": "logs/assistant.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
