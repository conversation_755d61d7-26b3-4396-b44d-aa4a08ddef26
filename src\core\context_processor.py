"""
Context processor for maintaining conversation context and memory.
"""

import logging
import json
import aiosqlite # Added import
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta


class ContextProcessor:
    """Manages conversation context and memory."""

    def __init__(self, db_manager, deepseek_client=None): # Added deepseek_client for summarization
        """Initialize the context processor."""
        self.db_manager = db_manager
        self.deepseek_client = deepseek_client # Store deepseek_client instance
        self.logger = logging.getLogger(__name__)

        # In-memory context cache
        self.session_contexts: Dict[str, List[Dict[str, Any]]] = {}

        # Context retention period (days)
        self.retention_days = 30

        # Maximum context length before summarization
        self.max_context_length = 20 # Reduced for more frequent summarization for testing

        # Number of messages to keep at start/end during heuristic summarization
        self.heuristic_summarization_keep_count = 5

        self.logger.info("Context processor initialized")

    async def update_context(
        self,
        message: str,
        session_id: str,
        is_assistant: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Update context with new message and store in database.

        Args:
            message: The message text
            session_id: The current session identifier
            is_assistant: Whether this is an assistant message (vs user)
            metadata: Optional metadata about the message

        Returns:
            Dictionary containing current context information
        """
        # Create context entry
        context_entry = {
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'is_assistant': is_assistant,
            'metadata': metadata or {}
        }

        # Initialize session context if needed
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = []
            # Try to load existing session from database
            await self._load_session(session_id)

        # Add to in-memory context
        self.session_contexts[session_id].append(context_entry)

        # Store in database (including metadata from context_entry)
        await self._store_message(session_id, context_entry, context_entry.get('metadata'))

        # Check if context needs summarization
        if len(self.session_contexts[session_id]) > self.max_context_length:
            await self._summarize_context(session_id)

        # Return current context
        return await self.get_context(session_id)

    async def get_context(self, session_id: str) -> Dict[str, Any]:
        """
        Get the current context for a session.

        Args:
            session_id: The session identifier

        Returns:
            Dictionary containing context information
        """
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = []
            await self._load_session(session_id)

        # Get recent messages (last 10)
        recent_messages = self.session_contexts[session_id][-10:]

        # Extract topics and entities if available
        topics = set()
        entities = set()

        for entry in self.session_contexts[session_id]:
            if 'metadata' in entry:
                if 'topics' in entry['metadata']:
                    topics.update(entry['metadata']['topics'])
                if 'entities' in entry['metadata']:
                    entities.update(entry['metadata']['entities'])

        return {
            'session_id': session_id,
            'recent_messages': recent_messages,
            'message_count': len(self.session_contexts[session_id]),
            'topics': list(topics),
            'entities': list(entities),
            'session_start': self.session_contexts[session_id][0]['timestamp'] if self.session_contexts[session_id] else None
        }

    async def save_session(self, session_id: str):
        """
        Save session context to database.

        Args:
            session_id: The session identifier
        """
        self.logger.info(f"Saving session context: {session_id}")

        if session_id not in self.session_contexts:
            return

        # All messages should already be stored individually
        # Just need to update session metadata
        session_metadata = {
            'message_count': len(self.session_contexts[session_id]),
            'last_updated': datetime.now().isoformat()
        }

        # Store session metadata
        async with aiosqlite.connect(self.db_manager.db_path) as db:
            await db.execute(
                """
                INSERT OR REPLACE INTO sessions 
                (session_id, metadata, created_at, updated_at) 
                VALUES (?, ?, ?, ?)
                """,
                (
                    session_id,
                    json.dumps(session_metadata),
                    self.session_contexts[session_id][0]['timestamp'] if self.session_contexts[session_id] else datetime.now(
                    ).isoformat(),
                    datetime.now().isoformat()
                )
            )
            await db.commit()

    async def _store_message(self, session_id: str, context_entry: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None):
        """
        Store a message in the database, including metadata.

        Args:
            session_id: The session identifier
            context_entry: The message context entry
            metadata: Optional metadata to store with the message
        """
        async with aiosqlite.connect(self.db_manager.db_path) as db:
            await db.execute(
                """
                INSERT INTO context 
                (session_id, message, is_assistant, timestamp, metadata) 
                VALUES (?, ?, ?, ?, ?)
                """, # Assuming 'metadata' column exists as TEXT/JSON
                (
                    session_id,
                    context_entry['message'],
                    context_entry['is_assistant'],
                    context_entry['timestamp'],
                    json.dumps(metadata) if metadata else None
                )
            )
            await db.commit()

    async def _load_session(self, session_id: str):
        """
        Load session context from database.

        Args:
            session_id: The session identifier
        """
        self.logger.debug(f"Loading session context: {session_id}")

        async with aiosqlite.connect(self.db_manager.db_path) as db:
            # Check if session exists
            cursor = await db.execute(
                "SELECT * FROM sessions WHERE session_id = ?",
                (session_id,)
            )
            session = await cursor.fetchone()

            if not session:
                return

            # Load messages
            cursor = await db.execute(
                """
                SELECT message, is_assistant, timestamp, metadata 
                FROM context 
                WHERE session_id = ? 
                ORDER BY timestamp ASC
                """, # Assuming 'metadata' column exists
                (session_id,)
            )
            messages = await cursor.fetchall()

            # Add to in-memory context
            for row in messages:
                loaded_metadata = json.loads(row[3]) if row[3] else {}
                self.session_contexts[session_id].append({
                    'message': row[0],
                    'is_assistant': bool(row[1]),
                    'timestamp': row[2],
                    'metadata': loaded_metadata
                })

    async def _summarize_context(self, session_id: str):
        """Summarize context if it gets too long."""
        current_context = self.session_contexts.get(session_id, [])
        if not current_context or len(current_context) <= self.max_context_length:
            return

        self.logger.info(f"Summarizing context for session {session_id}, current length: {len(current_context)}")

        # Placeholder for LLM-based summarization (requires deepseek_client and specific prompt)
        # if self.deepseek_client:
        #     try:
        #         # ... (LLM summarization logic as previously designed) ...
        #         # self.logger.info(f"LLM summarization complete for session {session_id}")
        #         # return
        #     except Exception as e:
        #         self.logger.error(f"Error during LLM summarization: {e}")

        # Fallback to heuristic summarization
        self.logger.info(f"Using heuristic summarization for session {session_id}")
        if len(current_context) > self.max_context_length:
            keep_start = self.heuristic_summarization_keep_count
            keep_end = self.heuristic_summarization_keep_count

            if len(current_context) <= keep_start + keep_end:
                return # Not enough messages to warrant summarization by this heuristic

            first_part = current_context[:keep_start]
            last_part = current_context[-keep_end:]
            middle_part = current_context[keep_start:-keep_end]

            middle_topics = set()
            middle_entities = set()
            for entry in middle_part:
                if 'metadata' in entry:
                    middle_topics.update(entry['metadata'].get('topics', []))
                    middle_entities.update(entry['metadata'].get('entities', []))
            
            summary_text = f"[Context Summary: Previous {len(middle_part)} messages summarized. Key topics: {', '.join(middle_topics) if middle_topics else 'N/A'}. Key entities: {', '.join(middle_entities) if middle_entities else 'N/A'}.]"
            
            summary_message_entry = {
                'message': summary_text,
                'timestamp': datetime.now().isoformat(),
                'is_assistant': True, # System generated
                'metadata': {'type': 'heuristic_summary', 'topics': list(middle_topics), 'entities': list(middle_entities)}
            }
            self.session_contexts[session_id] = first_part + [summary_message_entry] + last_part
            self.logger.info(f"Heuristic summarization applied. New length: {len(self.session_contexts[session_id])}")

            # Store this heuristic summary in the database as a regular message
            # This simplifies DB logic compared to a separate context_summaries table for this type of summary.
            # The 'type': 'heuristic_summary' in metadata distinguishes it.
            try:
                await self._store_message(session_id, summary_message_entry, summary_message_entry['metadata'])
                self.logger.info(f"Heuristic summary message stored in DB for session {session_id}")
                # TODO: Consider if old messages (middle_part) should be deleted from DB or marked as summarized.
                # For now, they remain, and the summary message is added. This could lead to redundancy if not handled.
            except Exception as e:
                self.logger.error(f"Failed to store heuristic summary message in DB: {e}")
            
            self.logger.warning("DB persistence of summarized context (deleting old messages) needs further review for heuristic summaries.")

    async def clear_old_sessions(self):
        """Clear sessions older than retention period."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        cutoff_str = cutoff_date.isoformat()

        self.logger.info(f"Clearing sessions older than: {cutoff_str}")

        async with aiosqlite.connect(self.db_manager.db_path) as db:
            # Delete old context entries
            await db.execute(
                "DELETE FROM context WHERE timestamp < ?",
                (cutoff_str,)
            )

            # Delete old sessions
            await db.execute(
                "DELETE FROM sessions WHERE updated_at < ?",
                (cutoff_str,)
            )

            # Delete old summaries
            await db.execute(
                "DELETE FROM context_summaries WHERE created_at < ?",
                (cutoff_str,)
            )

            await db.commit()
