"""
Mouse automation controller.
"""

import asyncio
import logging
from typing import Dict, Any
import pyautogui


class MouseController:
    """Handles mouse automation tasks."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the mouse controller."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = config.get('mouse_speed', 0.5)
        
        self.logger.info("Mouse controller initialized")
    
    async def execute(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a mouse command."""
        try:
            command = command.lower()
            
            if command == 'click':
                return await self._click(parameters)
            elif command == 'move':
                return await self._move(parameters)
            elif command == 'scroll':
                return await self._scroll(parameters)
            elif command == 'drag':
                return await self._drag(parameters)
            else:
                return {
                    'success': False,
                    'error': f'Unknown mouse command: {command}'
                }
                
        except Exception as e:
            self.logger.error(f"Mouse command error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _click(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Perform mouse click."""
        x = parameters.get('x')
        y = parameters.get('y')
        button = parameters.get('button', 'left')
        clicks = parameters.get('clicks', 1)
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.click(x, y, clicks=clicks, button=button)
        )
        
        return {
            'success': True,
            'message': f'Clicked at ({x}, {y}) with {button} button'
        }
    
    async def _move(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Move mouse cursor."""
        x = parameters.get('x')
        y = parameters.get('y')
        duration = parameters.get('duration', 0.5)
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.moveTo(x, y, duration=duration)
        )
        
        return {
            'success': True,
            'message': f'Moved mouse to ({x}, {y})'
        }
    
    async def _scroll(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Scroll mouse wheel."""
        clicks = parameters.get('clicks', 3)
        x = parameters.get('x')
        y = parameters.get('y')
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.scroll(clicks, x=x, y=y)
        )
        
        return {
            'success': True,
            'message': f'Scrolled {clicks} clicks'
        }
    
    async def _drag(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Drag mouse from one point to another."""
        x = parameters.get('x')
        y = parameters.get('y')
        duration = parameters.get('duration', 0.5)
        button = parameters.get('button', 'left')
        
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: pyautogui.drag(x, y, duration=duration, button=button)
        )
        
        return {
            'success': True,
            'message': f'Dragged to ({x}, {y})'
        }
