#!/usr/bin/env python3
"""
Siri-like AI Assistant
Main entry point for the AI assistant application.
"""

import asyncio
import logging
import sys
from pathlib import Path

from src.core.assistant import AIAssistant
from src.core.config_manager import ConfigManager
from src.ui.interface import UserInterface
from src.core.logger import setup_logging


def main():
    """Main entry point for the AI assistant."""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Starting AI Assistant...")
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Initialize the assistant
        assistant = AIAssistant(config)
        
        # Initialize the user interface
        ui = UserInterface(assistant, config)
        
        # Start the assistant
        logger.info("AI Assistant initialized successfully")
        ui.run()
        
    except KeyboardInterrupt:
        logger.info("Assistant stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
