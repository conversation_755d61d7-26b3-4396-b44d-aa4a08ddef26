# DeepSeek API Integration Guide

## Overview

This document provides comprehensive information about the DeepSeek API integration in the AI Assistant project. The integration features advanced capabilities including self-improvement, performance monitoring, and intelligent caching.

## Configuration

### Basic Configuration

The DeepSeek API is configured through the `config/config.json` file:

```json
{
  "deepseek": {
    "api_key": "your-api-key-here",
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat",
    "timeout": 30.0,
    "max_retries": 3,
    "min_request_interval": 0.5,
    "temperature": 0.3,
    "max_tokens": 300,
    "enable_caching": true,
    "enable_self_improvement": true,
    "performance_monitoring": true
  }
}
```

### Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `api_key` | string | required | Your DeepSeek API key |
| `base_url` | string | `https://api.deepseek.com/v1` | API base URL |
| `model` | string | `deepseek-chat` | Model to use |
| `timeout` | float | 30.0 | Request timeout in seconds |
| `max_retries` | int | 3 | Maximum retry attempts |
| `min_request_interval` | float | 0.5 | Minimum interval between requests |
| `temperature` | float | 0.3 | Response randomness (0.0-1.0) |
| `max_tokens` | int | 300 | Maximum tokens in response |
| `enable_caching` | bool | true | Enable response caching |
| `enable_self_improvement` | bool | true | Enable self-improvement features |
| `performance_monitoring` | bool | true | Enable performance monitoring |

## Features

### 1. Advanced Error Handling

The client implements sophisticated error handling with:
- Exponential backoff with jitter
- Circuit breaker pattern
- Adaptive retry strategies
- Graceful degradation

### 2. Performance Monitoring

Real-time monitoring includes:
- Response time tracking
- Success/error rate monitoring
- Token usage tracking
- Performance alerts
- Historical metrics storage

### 3. Self-Improvement

The client can evolve and optimize itself:
- Performance bottleneck analysis
- Code generation for optimizations
- Adaptive parameter tuning
- Learning from failures

### 4. Intelligent Caching

Smart caching system with:
- Content-based cache keys
- Adaptive TTL based on performance
- Cache hit rate optimization
- Memory-efficient storage

### 5. Rate Limiting

Sophisticated rate limiting:
- Adaptive rate adjustment
- Request queuing
- Burst handling
- API quota management

## Usage Examples

### Basic Usage

```python
from src.ai.deepseek_client import DeepSeekClient
from src.data.database_manager import DatabaseManager

# Initialize database manager
db_manager = DatabaseManager({"path": "data/assistant.db"})
await db_manager.initialize()

# Initialize client
config = {
    "api_key": "your-api-key",
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat"
}
client = DeepSeekClient(config, db_manager)

# Start the client
await client.start()

# Generate response
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
]
response = await client.generate_response(messages)

# Stop the client
await client.stop()
```

### Intent Analysis

```python
# Analyze user intent
context = {
    "session_id": "user-session-123",
    "recent_messages": [...],
    "topics": ["greeting", "status"]
}

result = await client.analyze_intent("How are you doing?", context)
print(f"Intent: {result['intent']}")
print(f"Response: {result['response']}")
print(f"Confidence: {result['confidence']}")
```

### Performance Monitoring

```python
# Get current performance metrics
metrics = client.performance_monitor.get_current_metrics()
print(f"Success rate: {metrics['success_rate']:.2%}")
print(f"Average response time: {metrics['avg_response_time']:.2f}s")

# Get recent alerts
alerts = client.performance_monitor.get_recent_alerts()
for alert in alerts:
    print(f"Alert: {alert['message']} ({alert['severity']})")

# Get performance summary
summary = client.performance_monitor.get_performance_summary()
```

## API Methods

### DeepSeekClient

#### `__init__(config: Dict[str, Any], db_manager=None)`
Initialize the DeepSeek client with configuration and optional database manager.

#### `async start()`
Start the client session and initialize connections.

#### `async stop()`
Stop the client and clean up resources.

#### `async generate_response(messages: List[Dict], **kwargs) -> Dict[str, Any]`
Generate a response from the API.

**Parameters:**
- `messages`: List of message dictionaries
- `system_prompt`: Optional system prompt
- `max_tokens`: Maximum tokens in response
- `temperature`: Response randomness
- `retry_count`: Number of retries

**Returns:**
- Dictionary with response data and metadata

#### `async analyze_intent(user_input: str, context: Dict) -> Dict[str, Any]`
Analyze user intent and generate appropriate response.

**Parameters:**
- `user_input`: User's input text
- `context`: Conversation context

**Returns:**
- Dictionary with intent analysis results

### PerformanceMonitor

#### `async record_api_call(...)`
Record an API call for performance tracking.

#### `get_current_metrics() -> Dict[str, Any]`
Get current performance metrics.

#### `get_recent_alerts(limit: int = 10) -> List[Dict]`
Get recent performance alerts.

#### `async get_historical_metrics(start_date, end_date) -> List[Dict]`
Get historical metrics from database.

## Database Schema

### API Metrics Table

```sql
CREATE TABLE api_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    response_time REAL NOT NULL,
    status_code INTEGER,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    timestamp TEXT NOT NULL,
    model_used TEXT,
    tokens_used INTEGER
);
```

### Context Table

```sql
CREATE TABLE context (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    message TEXT NOT NULL,
    is_assistant BOOLEAN NOT NULL,
    timestamp TEXT NOT NULL,
    metadata TEXT
);
```

### Sessions Table

```sql
CREATE TABLE sessions (
    session_id TEXT PRIMARY KEY,
    metadata TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    start_time TEXT,
    end_time TEXT
);
```

## Error Handling

### Common Errors

1. **Authentication Error (401)**
   - Check API key validity
   - Verify API key permissions

2. **Rate Limit Error (429)**
   - Automatic retry with backoff
   - Adjust `min_request_interval`

3. **Server Error (5xx)**
   - Automatic retry with exponential backoff
   - Circuit breaker activation

4. **Timeout Error**
   - Increase `timeout` value
   - Check network connectivity

### Error Response Format

```python
{
    "success": False,
    "error": "Error description",
    "status_code": 500,
    "timestamp": "2024-01-01T12:00:00Z",
    "retry_count": 3
}
```

## Performance Optimization

### Best Practices

1. **Enable Caching**
   - Set `enable_caching: true`
   - Monitor cache hit rates

2. **Optimize Request Frequency**
   - Adjust `min_request_interval`
   - Use batch processing when possible

3. **Monitor Performance**
   - Enable `performance_monitoring`
   - Set appropriate thresholds

4. **Use Self-Improvement**
   - Enable `enable_self_improvement`
   - Monitor generated optimizations

### Performance Thresholds

```json
{
  "response_time_threshold": 5.0,
  "error_rate_threshold": 0.1,
  "success_rate_threshold": 0.9
}
```

## Testing

### Running Tests

```bash
# Run all DeepSeek integration tests
pytest tests/test_deepseek_integration.py -v

# Run specific test
pytest tests/test_deepseek_integration.py::TestDeepSeekClient::test_api_request_success -v

# Run with coverage
pytest tests/test_deepseek_integration.py --cov=src.ai.deepseek_client
```

### Test Configuration

Use the test configuration for safe testing:

```python
test_config = {
    "api_key": "test-key",
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat",
    "timeout": 10.0,
    "max_retries": 1,
    "enable_caching": False,
    "performance_monitoring": False
}
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all dependencies are installed
   - Check Python path configuration

2. **Database Errors**
   - Verify database file permissions
   - Check database schema is up to date

3. **API Connection Issues**
   - Verify internet connectivity
   - Check firewall settings
   - Validate API endpoint URL

4. **Performance Issues**
   - Monitor response times
   - Check error rates
   - Review performance alerts

### Debug Mode

Enable debug logging for detailed information:

```python
import logging
logging.getLogger('src.ai.deepseek_client').setLevel(logging.DEBUG)
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review error logs
3. Check performance metrics
4. Consult the API documentation
