"""
System operations controller.
"""

import asyncio
import logging
import subprocess
import os
from typing import Dict, Any


class SystemController:
    """Handles system-level operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the system controller."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("System controller initialized")
    
    async def execute(self, command: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a system command."""
        try:
            command = command.lower()
            
            if command == 'run_program':
                return await self._run_program(parameters)
            elif command == 'open_url':
                return await self._open_url(parameters)
            elif command == 'get_info':
                return await self._get_system_info(parameters)
            else:
                return {
                    'success': False,
                    'error': f'Unknown system command: {command}'
                }
                
        except Exception as e:
            self.logger.error(f"System command error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _run_program(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Run a program or application."""
        program = parameters.get('program', '')
        args = parameters.get('args', [])
        
        if not program:
            return {
                'success': False,
                'error': 'No program specified'
            }
        
        try:
            # Run the program
            process = await asyncio.create_subprocess_exec(
                program, *args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            return {
                'success': True,
                'message': f'Started program: {program}',
                'pid': process.pid
            }
            
        except FileNotFoundError:
            return {
                'success': False,
                'error': f'Program not found: {program}'
            }
    
    async def _open_url(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Open a URL in the default browser."""
        url = parameters.get('url', '')
        
        if not url:
            return {
                'success': False,
                'error': 'No URL specified'
            }
        
        try:
            import webbrowser
            webbrowser.open(url)
            
            return {
                'success': True,
                'message': f'Opened URL: {url}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to open URL: {e}'
            }
    
    async def _get_system_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get system information."""
        info_type = parameters.get('type', 'general')
        
        try:
            import platform
            import psutil
            
            if info_type == 'general':
                info = {
                    'platform': platform.platform(),
                    'processor': platform.processor(),
                    'memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                    'cpu_count': psutil.cpu_count()
                }
            elif info_type == 'memory':
                memory = psutil.virtual_memory()
                info = {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'percent_used': memory.percent
                }
            else:
                return {
                    'success': False,
                    'error': f'Unknown info type: {info_type}'
                }
            
            return {
                'success': True,
                'info': info
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to get system info: {e}'
            }
