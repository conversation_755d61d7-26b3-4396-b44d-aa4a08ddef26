"""
Intent manager for analyzing user requests and generating responses.
"""

import logging
from typing import Dict, Any


class IntentManager:
    """Manages intent analysis and response generation."""

    def __init__(self, deepseek_client):
        """Initialize the intent manager."""
        self.deepseek_client = deepseek_client
        self.logger = logging.getLogger(__name__)

        self.logger.info("Intent manager initialized")

    async def analyze_intent(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user intent and generate response."""
        try:
            # Use DeepSeek client to analyze intent
            result = await self.deepseek_client.analyze_intent(user_input, context)
            return result

        except Exception as e:
            self.logger.error(f"Error analyzing intent: {e}")
            return {
                'intent': 'error',
                'actions': [],
                'response': 'I apologize, but I encountered an error understanding your request.',
                'confidence': 0.0,
                'requires_confirmation': False
            }
