"""
Intent manager for analyzing user requests and generating responses.
"""

import logging
from typing import Dict, Any
from .fallback_manager import FallbackManager


class IntentManager:
    """Manages intent analysis and response generation."""

    def __init__(self, deepseek_client, config: Dict[str, Any] = None):
        """Initialize the intent manager."""
        self.deepseek_client = deepseek_client
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # Initialize fallback manager
        self.fallback_manager = FallbackManager(self.config)

        # Track API availability
        self.api_available = True
        self.consecutive_failures = 0
        self.max_failures_before_fallback = 3

        self.logger.info("Intent manager initialized with fallback support")

    async def analyze_intent(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user intent and generate response with fallback support."""
        try:
            # Check if we should try API or use fallback
            if self.api_available and self.consecutive_failures < self.max_failures_before_fallback:
                # Try DeepSeek client
                result = await self.deepseek_client.analyze_intent(user_input, context)

                # Check if the result indicates API failure
                if not result.get('success', True):
                    self.consecutive_failures += 1
                    self.logger.warning(
                        f"API failure #{self.consecutive_failures}: {result.get('error', 'Unknown error')}")

                    # If too many failures, switch to fallback mode
                    if self.consecutive_failures >= self.max_failures_before_fallback:
                        self.api_available = False
                        self.logger.warning(
                            "Switching to fallback mode due to consecutive API failures")
                        return await self._get_fallback_response(user_input, context, "api_failure")
                    else:
                        # Try fallback for this request but keep API available
                        return await self._get_fallback_response(user_input, context, "temporary_failure")
                else:
                    # Success - reset failure counter
                    self.consecutive_failures = 0
                    if not self.api_available:
                        self.api_available = True
                        self.logger.info(
                            "API is back online - switching back from fallback mode")
                    return result
            else:
                # Use fallback mode
                return await self._get_fallback_response(user_input, context, "fallback_mode")

        except Exception as e:
            self.logger.error(f"Error analyzing intent: {e}")
            self.consecutive_failures += 1
            return await self._get_fallback_response(user_input, context, "exception")

    async def _get_fallback_response(self, user_input: str, context: Dict[str, Any], error_type: str) -> Dict[str, Any]:
        """Get fallback response when API is unavailable."""
        try:
            fallback = self.fallback_manager.get_fallback_response(
                user_input, context, error_type)

            return {
                'intent': 'fallback',
                'actions': [],
                'response': fallback.response,
                'confidence': fallback.confidence,
                'requires_confirmation': False,
                'fallback_source': fallback.source,
                'api_available': self.api_available,
                'timestamp': fallback.timestamp.isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error in fallback response: {e}")
            return {
                'intent': 'error',
                'actions': [],
                'response': 'I apologize, but I encountered an error understanding your request.',
                'confidence': 0.0,
                'requires_confirmation': False,
                'fallback_source': 'emergency',
                'api_available': False
            }

    def get_status(self) -> Dict[str, Any]:
        """Get current status of intent manager."""
        return {
            'api_available': self.api_available,
            'consecutive_failures': self.consecutive_failures,
            'max_failures_threshold': self.max_failures_before_fallback,
            'fallback_analytics': self.fallback_manager.get_response_analytics()
        }

    def reset_api_status(self):
        """Reset API status (useful for manual recovery)."""
        self.api_available = True
        self.consecutive_failures = 0
        self.logger.info("API status manually reset")
